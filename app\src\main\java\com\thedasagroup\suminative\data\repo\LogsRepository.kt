package com.thedasagroup.suminative.data.repo

import com.thedasagroup.suminative.data.api.UPLOAD_FILES_URL
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.prefs.Prefs
import io.ktor.client.request.forms.formData
import io.ktor.client.request.forms.submitFormWithBinaryData
import io.ktor.http.ContentType
import io.ktor.http.Headers
import io.ktor.http.HttpHeaders
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

class LogsRepository(val prefs: Prefs) : BaseRepository() {
    
    /**
     * Uploads a log file to the server using Ktor
     * 
     * @param file The log file to upload
     * @param deviceId Unique identifier for the device
     * @param timestamp Timestamp for the log file
     * @return Result of the upload operation
     */
    suspend fun uploadLogFile(file: File, deviceId: String, timestamp: String): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            // Prepare the endpoint
            val uploadEndpoint = UPLOAD_FILES_URL
            
            // Create multipart form data for file upload
            val response = apiClient.submitFormWithBinaryData(
                url = uploadEndpoint,
                formData = formData {
                    // Add file part
                    append("file", file.readBytes(), Headers.build {
                        append(HttpHeaders.ContentDisposition, "filename=${file.name}")
                        append(HttpHeaders.ContentType, ContentType.Application.OctetStream.toString())
                    })
                    // Add metadata
                    append("storeId", prefs.store?.id?.toString() ?: "")
                    append("deviceUuid", deviceId)
                    append("deviceType", "POS_Android")
                }
            )
            
            // Check if the upload was successful
            if (response.status.value in 200..299) {
                Result.success(true)
            } else {
                Result.failure(Exception("Failed to upload log file. Server responded with ${response.status.value}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}