package com.thedasagroup.suminative.ui.products

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class DownloadProductsViewModel @AssistedInject constructor(
    @Assisted state: DownloadProductsState,
    private val downloadProductsUseCase: DownloadProductsUseCase
) : MavericksViewModel<DownloadProductsState>(state) {

    init {
        checkExistingProducts()
    }

    private fun checkExistingProducts() {
        suspend {
            downloadProductsUseCase.checkExistingProducts().execute { result ->
                when (result) {
                    is Success -> {
                        copy(hasExistingProducts = result()() ?: false)
                    }
                    else -> {
                        copy(hasExistingProducts = false)
                    }
                }
            }
        }
    }

    suspend fun downloadProducts(): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        
        setState { copy(downloadResponse = Loading()) }
        
        downloadProductsUseCase().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        downloadResponse = it(),
                        hasExistingProducts = it()() ?: false
                    )
                }
                else -> {
                    flow.value = Success(false)
                    copy(downloadResponse = Success(false))
                }
            }
        }
        
        return flow
    }

    fun clearError() {
        setState { copy(errorMessage = "") }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<DownloadProductsViewModel, DownloadProductsState> {
        override fun create(state: DownloadProductsState): DownloadProductsViewModel
    }

    companion object :
        MavericksViewModelFactory<DownloadProductsViewModel, DownloadProductsState> by hiltMavericksViewModelFactory()
}

data class DownloadProductsState(
    val downloadResponse: Async<Boolean> = Uninitialized,
    val hasExistingProducts: Boolean = false,
    val lastUpdateTime : Long = 0L,
    val productCount: Long = 0,
    val errorMessage: String = ""
) : MavericksState 