package com.thedasagroup.suminative.data.model.response.store_orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Cart(
    @SerialName("discount")
    val discount: Double? = null,
    @SerialName("extraPrice")
    val extraPrice: Double? = null,
    @SerialName("netPayable")
    val netPayable: Double? = null,
    @SerialName("optionPrice")
    val optionPrice: Double? = null,
    @SerialName("price")
    val price: Double? = null,
    @SerialName("quantity")
    val quantity: Int? = null,
    @SerialName("storeItem")
    val storeItem: StoreItem? = null,
    @SerialName("tax")
    val tax: Double? = null,
    @SerialName("orderNotes")
    val orderNotes: String? = null,
)