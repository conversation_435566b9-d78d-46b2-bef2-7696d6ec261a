CREATE TABLE OptionSetEntity (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    optionSetId INTEGER, -- Remote server ID
    productId INTEGER NOT NULL, -- Link to ProductEntity.productId
    name TEXT,
    condition INTEGER, -- 1 = select one, 2 = select any number
    customNumber INTEGER,
    displayOrder INTEGER,
    itemId INTEGER,
    status INTEGER,
    variantType INTEGER,
    storeId INTEGER NOT NULL,
    createdAt INTEGER NOT NULL, -- Local timestamp
    updatedAt INTEGER NOT NULL, -- Local timestamp
    synced INTEGER NOT NULL DEFAULT 0, -- 0 = not synced, 1 = synced
    
    FOREIGN KEY(productId) REFERENCES ProductEntity(productId)
);

-- Indexes for better performance
CREATE UNIQUE INDEX idx_optionset_id_unique ON OptionSetEntity(optionSetId);
CREATE INDEX idx_optionset_product ON OptionSetEntity(productId);
CREATE INDEX idx_optionset_store ON OptionSetEntity(storeId);
CREATE INDEX idx_optionset_synced ON OptionSetEntity(synced);
CREATE INDEX idx_optionset_display_order ON OptionSetEntity(displayOrder);

-- Queries for OptionSet operations

insertOptionSet:
INSERT INTO OptionSetEntity (
    optionSetId, productId, name, condition, customNumber, displayOrder,
    itemId, status, variantType, storeId, createdAt, updatedAt, synced
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

insertOrReplaceOptionSet:
INSERT OR REPLACE INTO OptionSetEntity (
    optionSetId, productId, name, condition, customNumber, displayOrder,
    itemId, status, variantType, storeId, createdAt, updatedAt, synced
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

getOptionSetsByProduct:
SELECT * FROM OptionSetEntity WHERE productId = ? ORDER BY displayOrder ASC;

getOptionSetsByStore:
SELECT * FROM OptionSetEntity WHERE storeId = ? ORDER BY displayOrder ASC;

getOptionSetById:
SELECT * FROM OptionSetEntity WHERE optionSetId = ?;

updateOptionSet:
UPDATE OptionSetEntity 
SET name = ?, condition = ?, customNumber = ?, displayOrder = ?,
    status = ?, variantType = ?, updatedAt = ?, synced = ?
WHERE optionSetId = ?;

deleteOptionSetsByProduct:
DELETE FROM OptionSetEntity WHERE productId = ?;

deleteAllOptionSets:
DELETE FROM OptionSetEntity;

-- Count queries
countOptionSetsByProduct:
SELECT COUNT(*) FROM OptionSetEntity WHERE productId = ?;

countTotalOptionSets:
SELECT COUNT(*) FROM OptionSetEntity; 