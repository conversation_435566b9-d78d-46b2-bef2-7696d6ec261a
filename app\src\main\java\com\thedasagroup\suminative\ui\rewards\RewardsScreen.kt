package com.thedasagroup.suminative.ui.rewards

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.ImeAction
import kotlinx.coroutines.launch

// Theme colors
private val ThemeGreen = Color(0xFF2E7D32)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RewardsScreen(
    viewModel: RewardsViewModel,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val state by viewModel.collectAsState()
    var customerIdInput by remember { mutableStateOf("") }
    var showAddPointsDialog by remember { mutableStateOf(false) }
    var selectedCustomer by remember { mutableStateOf<RewardsCustomer?>(null) }
    val coroutineScope = rememberCoroutineScope()
    
    // Get business ID from preferences for placeholder
    val businessId = viewModel.prefs.loginResponse?.businesses?.id ?: 12
    val placeholderText = "dasa-$businessId-987"
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp)
    ) {
        // Top App Bar
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 24.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = Color.Black
                )
            }
            Text(
                text = "Customer Rewards",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                modifier = Modifier.padding(start = 8.dp)
            )
        }
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            // Customer ID Input Section
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Search Customer",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color.Black,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    OutlinedTextField(
                        value = customerIdInput,
                        onValueChange = { customerIdInput = it },
                        label = { Text("Customer ID") },
                        placeholder = { Text(placeholderText) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Text,
                            imeAction = ImeAction.Search
                        ),
                        singleLine = true
                    )
                    
                    Button(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp),
                        onClick = {
                            if (customerIdInput.isNotEmpty() && !state.isLoading) {
                                // Extract customer ID from input format (dasa-businessId-customerId)
                                val customerId = extractCustomerId(customerIdInput)
                                if (customerId != null) {
                                    coroutineScope.launch {
                                        viewModel.getAllCustomers(customerId)
                                    }
                                }
                            }
                        },
                        colors = ButtonDefaults.buttonColors(containerColor = ThemeGreen),
                        shape = RoundedCornerShape(12.dp),
                        enabled = !state.isLoading
                    ) {
                        Text(
                            text = if (state.isLoading) "Searching..." else "Search Customer",
                            color = Color.White,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            // Customer Info Section
            state.getAllCustomersResponse.invoke()?.let { response ->
                if (response.success == true && !response.data.isNullOrEmpty()) {
                    val customer = response.data.first()
                    selectedCustomer = customer
                    
                    CustomerInfoCard(
                        customer = customer,
                        onAddPointsClick = {
                            showAddPointsDialog = true
                        }
                    )
                } else {
                    // Show error or no customer found
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFEBEE))
                    ) {
                        Text(
                            text = "No customer found or invalid customer ID",
                            modifier = Modifier.padding(16.dp),
                            color = Color(0xFFD32F2F)
                        )
                    }
                }
            }
            
            // Loading indicator
            if (state.isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
        }
    }
    
    // Add Points Dialog
    if (showAddPointsDialog && selectedCustomer != null) {
        AddPointsDialog(
            customer = selectedCustomer!!,
            viewModel = viewModel,
            onDismiss = { 
                showAddPointsDialog = false
                viewModel.clearAddPointsResponse()
            }
        )
    }
}

@Composable
fun CustomerInfoCard(
    customer: RewardsCustomer,
    onAddPointsClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 16.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFE8F5E8))
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Customer Information",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color.Black,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            CustomerInfoRow("Name", customer.name ?: "N/A")
            CustomerInfoRow("Email", customer.email ?: "N/A")
            CustomerInfoRow("Phone", customer.phone ?: "N/A")
            CustomerInfoRow("Customer ID", customer.id?.toString() ?: "N/A")
            CustomerInfoRow("Business ID", customer.businessId?.toString() ?: "N/A")
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp),
                onClick = onAddPointsClick,
                colors = ButtonDefaults.buttonColors(containerColor = ThemeGreen),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = "Add Reward Points",
                    color = Color.White,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
fun CustomerInfoRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = "$label:",
            fontWeight = FontWeight.Medium,
            color = Color.Black,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            color = Color.Black,
            modifier = Modifier.weight(2f)
        )
    }
}

// Helper function to extract customer ID from input format
private fun extractCustomerId(input: String): Int? {
    return try {
        // Expected format: dasa-businessId-customerId
        val parts = input.split("-")
        if (parts.size == 3 && parts[0] == "dasa") {
            parts[2].toIntOrNull()
        } else {
            // If not in expected format, try to parse as direct number
            input.toIntOrNull()
        }
    } catch (e: Exception) {
        null
    }
}
