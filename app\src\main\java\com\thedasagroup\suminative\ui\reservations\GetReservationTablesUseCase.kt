package com.thedasagroup.suminative.ui.reservations

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.response.reservations.Table
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * Use case for fetching reservation tables for a specific area
 * 
 * This use case handles the business logic for retrieving tables
 * within a specific area that can be reserved.
 */
class GetReservationTablesUseCase @Inject constructor(
    private val reservationsRepository: ReservationsRepository
) {
    /**
     * Fetch reservation tables for a specific area
     * 
     * @param areaId The area ID to fetch tables for
     * @return StateFlow containing the async result with list of tables
     */
    suspend operator fun invoke(
        areaId: Int
    ): StateFlow<Async<List<Table>>> {
        return reservationsRepository.getReservationTables(areaId)
    }
}
