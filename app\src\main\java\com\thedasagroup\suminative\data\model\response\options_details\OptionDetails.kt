package com.thedasagroup.suminative.data.model.response.options_details

import com.thedasagroup.suminative.data.model.request.order.OptionSet
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OptionDetails(
    @SerialName("command") val command: String? = null,
    @SerialName("optionSets") val optionSets: List<OptionSet>? = mutableListOf(),
    @SerialName("paymentId") val paymentId: Int? = 0,
    @SerialName("paymentResponseJson") val paymentResponseJson: String? = null,
    @SerialName("socketId") val socketId: String? = null,
    @SerialName("success") val success: Boolean? = false
)