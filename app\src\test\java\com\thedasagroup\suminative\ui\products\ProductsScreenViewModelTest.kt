package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for table-specific cart functionality in ProductsScreenViewModel
 */
class ProductsScreenViewModelTest {

    @Test
    fun `test getCurrentTableOrder returns global order when no tables selected`() {
        val state = ProductsScreenState(
            order = Order(totalPrice = 100.0),
            selectedTables = emptyList(),
            selectedTableIndex = 0
        )
        
        val currentOrder = state.getCurrentTableOrder()
        assertEquals(100.0, currentOrder.totalPrice, 0.0)
    }

    @Test
    fun `test getCurrentTableOrder returns table-specific order when table selected`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )
        
        val table1Order = Order(totalPrice = 50.0)
        val globalOrder = Order(totalPrice = 100.0)
        
        val state = ProductsScreenState(
            order = globalOrder,
            selectedTables = listOf(table1),
            selectedTableIndex = 0,
            tableOrders = mapOf(101 to table1Order)
        )
        
        val currentOrder = state.getCurrentTableOrder()
        assertEquals(50.0, currentOrder.totalPrice, 0.0)
    }

    @Test
    fun `test getCurrentTableOrder returns empty order when table not found in tableOrders`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )
        
        val globalOrder = Order(totalPrice = 100.0)
        
        val state = ProductsScreenState(
            order = globalOrder,
            selectedTables = listOf(table1),
            selectedTableIndex = 0,
            tableOrders = emptyMap() // No table orders
        )
        
        val currentOrder = state.getCurrentTableOrder()
        assertEquals(0.0, currentOrder.totalPrice ?: 0.0, 0.0)
    }

    @Test
    fun `test getCurrentTableId returns null when no tables selected`() {
        val state = ProductsScreenState(
            selectedTables = emptyList(),
            selectedTableIndex = 0
        )
        
        val currentTableId = state.getCurrentTableId()
        assertNull(currentTableId)
    }

    @Test
    fun `test getCurrentTableId returns correct table ID when table selected`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )
        
        val state = ProductsScreenState(
            selectedTables = listOf(table1),
            selectedTableIndex = 0
        )
        
        val currentTableId = state.getCurrentTableId()
        assertEquals(101, currentTableId)
    }

    @Test
    fun `test getCurrentTableId returns correct table ID for different selected index`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )
        
        val table2 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 102,
            tableName = "Table 2",
            tableCapacity = 6
        )
        
        val state = ProductsScreenState(
            selectedTables = listOf(table1, table2),
            selectedTableIndex = 1 // Select second table
        )
        
        val currentTableId = state.getCurrentTableId()
        assertEquals(102, currentTableId)
    }

    @Test
    fun `test getCurrentTableId returns null when selectedTableIndex is out of bounds`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )
        
        val state = ProductsScreenState(
            selectedTables = listOf(table1),
            selectedTableIndex = 5 // Out of bounds
        )
        
        val currentTableId = state.getCurrentTableId()
        assertNull(currentTableId)
    }
}
