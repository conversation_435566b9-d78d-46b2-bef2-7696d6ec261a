package com.thedasagroup.suminative.di

import com.thedasagroup.suminative.data.database.CategoryRepository
import com.thedasagroup.suminative.data.database.DatabaseManager
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.domain.categories.SyncCategoriesUseCase
import com.thedasagroup.suminative.ui.stock.CategorySortingHelper
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object CategoryModule {

    @Provides
    @Singleton
    fun provideCategoryRepository(
        databaseManager: DatabaseManager
    ): CategoryRepository {
        return CategoryRepository(databaseManager)
    }

    @Provides
    @Singleton
    fun providesCategoriesHelper(
        stockRepository: StockRepository,
        categoryRepository: CategoryRepository,
        prefs: Prefs
    ) : CategorySortingHelper{
        return CategorySortingHelper(
            stockRepository = stockRepository,
            categoryRepository = categoryRepository,
            prefs = prefs
        )
    }

    @Provides
    @Singleton
    fun provideSyncCategoriesUseCase(
        stockRepository: StockRepository,
        categoryRepository: CategoryRepository,
        prefs: Prefs
    ): SyncCategoriesUseCase {
        return SyncCategoriesUseCase(stockRepository, categoryRepository, prefs)
    }
} 