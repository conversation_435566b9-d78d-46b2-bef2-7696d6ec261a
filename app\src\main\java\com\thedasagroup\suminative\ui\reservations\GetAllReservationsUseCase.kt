package com.thedasagroup.suminative.ui.reservations

import android.annotation.SuppressLint
import com.airbnb.mvrx.Async
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_APP
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_RESERVATIONS
import kotlinx.coroutines.flow.StateFlow
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject

class GetAllReservationsUseCase @Inject constructor(
    private val reservationsRepository: ReservationsRepository,
    private val prefs: Prefs,
    private val trueTimeImpl: TrueTimeImpl
) {
    @SuppressLint("SimpleDateFormat")
    suspend operator fun invoke(): StateFlow<Async<ReservationsResponse>> {
        val storeId = prefs.store?.id ?: 0
        val now = trueTimeImpl.now()
        val currentTime = SimpleDateFormat(DATE_FORMAT_RESERVATIONS).format(now)
        return reservationsRepository.getAllReservationsRetrofit(storeId, currentTime)
    }
}
