package com.thedasagroup.suminative.ui.payment

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.CreditCard
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.compose.mavericksViewModel
import com.airbnb.mvrx.viewModel
import com.sumup.merchant.reader.models.TransactionInfo
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import com.thedasagroup.suminative.ui.products.CATEGORY_GREEN_COLOR
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.transformDecimal
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

@AndroidEntryPoint
class SumUpPaymentActivity : AppCompatActivity(), MavericksView {
    private val viewModel: SumUpPaymentViewModel by viewModel()
    private val paymentViewModel : PaymentViewModel by viewModel()

    var order: Order? = null
    
    override fun invalidate() {
        // Called when the state is updated
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        android.util.Log.d("SumUpPaymentActivity", "onCreate started")

        try {
            android.util.Log.d("SumUpPaymentActivity", "Getting order from intent")
            val order = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                intent.getParcelableExtra(ARG_ORDER, Order::class.java)
            } else {
                @Suppress("DEPRECATION")
                intent.getParcelableExtra(ARG_ORDER)
            } ?: Order()

            this.order = order

            android.util.Log.d("SumUpPaymentActivity", "Order received: ${order.id}")

            setContent {
                android.util.Log.d("SumUpPaymentActivity", "Setting content")
                SumiNativeTheme {
                    SumUpPaymentScreen(
                        order = order,
                        onFinish = { resultOrder ->
                            try {
                                android.util.Log.d("SumUpPaymentActivity", "Finishing activity with result: $resultOrder")
                                val resultIntent = Intent().apply {
                                    if (resultOrder != null) {
                                        putExtra(RESULT_ORDER, resultOrder)
                                    }
                                }
                                setResult(if (resultOrder != null) RESULT_OK else RESULT_CANCELED, resultIntent)
                                finish()
                            } catch (e: Exception) {
                                android.util.Log.e("SumUpPaymentActivity", "Error finishing activity", e)
                                finish()
                            }
                        },
                        paymentViewModel = paymentViewModel
                    )
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("SumUpPaymentActivity", "Error in onCreate", e)
            Toast.makeText(this, "Error initializing SumUp payment", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        // Handle SumUp activity results
        when (requestCode) {
            SumUpPaymentHelper.REQUEST_CODE_SUMUP_LOGIN -> {
                handleLoginResult(data)
            }
            SumUpPaymentHelper.REQUEST_CODE_SUMUP_PAYMENT -> {
                handlePaymentResult(data)
            }
        }
    }

    private fun handleLoginResult(data: Intent?) {
        if (data != null) {
            val extra = data.extras
            val resultCode = extra?.getInt(com.sumup.merchant.reader.api.SumUpAPI.Response.RESULT_CODE)
            val message = extra?.getString(com.sumup.merchant.reader.api.SumUpAPI.Response.MESSAGE) ?: "Unknown error"

            val success = resultCode == com.sumup.merchant.reader.api.SumUpAPI.Response.ResultCode.SUCCESSFUL
            viewModel.onLoginResult(success, message)
        } else {
            viewModel.onLoginResult(false, "Login cancelled")
        }
    }

    private fun handlePaymentResult(data: Intent?) {
        if (data != null) {
            val extra = data.extras
            val resultCode = extra?.getInt(com.sumup.merchant.reader.api.SumUpAPI.Response.RESULT_CODE)
            val message = extra?.getString(com.sumup.merchant.reader.api.SumUpAPI.Response.MESSAGE) ?: "Unknown error"
            val transactionInfo = extra?.getParcelable<TransactionInfo>(com.sumup.merchant.reader.api.SumUpAPI.Response.TX_INFO)

            val success = resultCode == com.sumup.merchant.reader.api.SumUpAPI.Response.ResultCode.SUCCESSFUL

            lifecycleScope.launch {
                if (success && transactionInfo != null) {
                    val updatedOrder = order?.copy( paymentType = 6, transactionId = transactionInfo.transactionCode ?: "")
                    viewModel.onPaymentResult(success, updatedOrder, transactionInfo, message)
//                    paymentViewModel.placeOnlineOrder(order = updatedOrder ?: Order()).collectLatest {orderResponse ->
//                        if (orderResponse is Success) {
//                            viewModel.onPaymentResult(success,
//                                orderResponse().order, transactionInfo, message)
//                        }
//                    }
                }
            }
        } else {
            viewModel.onPaymentResult(false, null, null, "Payment cancelled")
        }
    }

    companion object {
        private const val ARG_ORDER = "extra_order"
        const val RESULT_ORDER = "result_order"

        fun createIntent(context: Context, order: Order): Intent {
            return Intent(context, SumUpPaymentActivity::class.java).apply {
                putExtra(ARG_ORDER, order)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SumUpPaymentScreen(
    order: Order,
    onFinish: (Order?) -> Unit,
    paymentViewModel: PaymentViewModel
) {
    val orderResponse by paymentViewModel.collectAsState(PaymentState::paymentOrderResponse)

    android.util.Log.d("SumUpPaymentScreen", "Composable started")
    
    val viewModel: SumUpPaymentViewModel = mavericksViewModel()
    val state by viewModel.collectAsState()
    val context = LocalContext.current

    android.util.Log.d("SumUpPaymentScreen", "State: isLoggedIn=${state.isLoggedIn}, isLoading=${state.isLoading}")

    // Set the order in the view model
    LaunchedEffect(order) {
        viewModel.setOrder(order)
    }

    // Handle finish when payment is successful
    LaunchedEffect(state.shouldFinish) {
        if (state.shouldFinish) {
            onFinish(state.resultOrder)
        }
    }

    // Show error messages
    LaunchedEffect(state.errorMessage) {
        state.errorMessage?.let { message ->
            Toast.makeText(context, message, Toast.LENGTH_LONG).show()
            viewModel.clearMessages()
        }
    }

    // Show success messages
    LaunchedEffect(state.successMessage) {
        state.successMessage?.let { message ->
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
            viewModel.clearMessages()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "SumUp Payment",
                        fontFamily = fontPoppins,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { onFinish(null) }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFF2E7D32),
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(Color(0xFFF5F5F5))
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // SumUp Logo/Icon
                Card(
                    modifier = Modifier.size(120.dp),
                    shape = RoundedCornerShape(60.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.CreditCard,
                            contentDescription = "SumUp",
                            modifier = Modifier.size(60.dp),
                            tint = Color(0xFF2E7D32)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))

                // Order Details
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Order Total",
                            fontSize = 16.sp,
                            fontFamily = fontPoppins,
                            color = Color.Black.copy(alpha = 0.7f)
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "£${(order.totalPrice ?: 0.0).transformDecimal()}",
                            fontSize = 32.sp,
                            fontWeight = FontWeight.Bold,
                            fontFamily = fontPoppins,
                            color = Color(0xFF2E7D32)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))

                // Action Buttons
                if (state.isLoading) {
                    CircularProgressIndicator(
                        color = Color(0xFF2E7D32),
                        modifier = Modifier.size(48.dp)
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "Processing...",
                        fontSize = 16.sp,
                        fontFamily = fontPoppins,
                        color = Color.Black.copy(alpha = 0.7f)
                    )
                } else if (!state.isLoggedIn) {
                    // Show login button
                    Button(
                        onClick = {
                            viewModel.startLogin()
                            SumUpPaymentHelper.startLogin(context as SumUpPaymentActivity)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32),
                            contentColor = Color.White
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            text = "Login to SumUp",
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp,
                            fontFamily = fontPoppins
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "Please login to your SumUp account to process the payment",
                        fontSize = 14.sp,
                        fontFamily = fontPoppins,
                        color = Color.Black.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center
                    )
                } else {

                    // Show payment button
                    Button(
                        onClick = {
                            viewModel.startPayment()
                            SumUpPaymentHelper.startPayment(context as SumUpPaymentActivity, order)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32),
                            contentColor = Color.White
                        ),
                        shape = RoundedCornerShape(12.dp),
                        enabled = if(orderResponse is Loading) false else true
                    ) {
                        if(orderResponse is Loading){
                            CircularProgressIndicator(color = Color(CATEGORY_GREEN_COLOR))
                        }
                        else {
                            Text(
                                text = "Process Payment",
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp,
                                fontFamily = fontPoppins
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Merchant info
                    SumUpPaymentHelper.getCurrentMerchantInfo()?.let { merchantInfo ->
                        Text(
                            text = merchantInfo,
                            fontSize = 12.sp,
                            fontFamily = fontPoppins,
                            color = Color.Black.copy(alpha = 0.5f),
                            textAlign = TextAlign.Center
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Cancel button
                OutlinedButton(
                    onClick = { onFinish(null) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = "Cancel",
                        fontFamily = fontPoppins,
                        color = Color.Black.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}
