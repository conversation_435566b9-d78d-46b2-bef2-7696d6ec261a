package com.thedasagroup.suminative.data.model.response.stock

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class StockItemsResponse(
    @SerialName("items") val items: List<StockItem>? = mutableListOf(),
    @SerialName("totalCount") val totalCount: Int? = 0,
    val success: Boolean = false
) {
    val mapCategories: Map<String?, List<StockItem>>?
        get() = items?.groupBy { it.category }?.filterValues { it.isNotEmpty() }
}