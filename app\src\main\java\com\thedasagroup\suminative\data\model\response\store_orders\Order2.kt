package com.thedasagroup.suminative.data.model.response.store_orders

import android.os.Parcelable
import com.airbnb.mvrx.PersistState
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

@Serializable
@Parcelize
data class Order2(
    @PersistState @SerialName("businessId") val businessId: Int? = 0,
    @PersistState @SerialName("cartJson") val cartJson: String? = null,
    @PersistState @SerialName("createdBy") val createdBy: Int? = 0,
    @PersistState @SerialName("createdOn") val createdOn: String? = null,
    @PersistState @SerialName("customerId") val customerId: Int? = 0,
    @PersistState @SerialName("deliveryCharges") val deliveryCharges: Double? = 0.0,
    @PersistState @SerialName("deliveryNote") val deliveryNote: String? = null,
    @PersistState @SerialName("deliveryType") val deliveryType: Int? = 1,
    @PersistState @SerialName("discountOnPromo") val discountOnPromo: Double? = 0.0,
    @PersistState @SerialName("id") val id: Int? = 0,
    @PersistState @SerialName("isGuest") val isGuest: Boolean? = false,
    @PersistState @SerialName("isScheduled") val isScheduled: Boolean? = false,
    @PersistState @SerialName("json") val json: String? = null,
    @PersistState @SerialName("netPayable") val netPayable: Double? = 0.0,
    @PersistState @SerialName("paymentType") val paymentType: Int? = 0,
    @PersistState @SerialName("pickupTime") val pickupTime: String? = null,
    @PersistState @SerialName("promoId") val promoId: Int? = 0,
    @PersistState @SerialName("scheduleInt") val scheduleInt: Int? = 0,
    @PersistState @SerialName("scheduledDateTime") val scheduledDateTime: String? = null,
    @PersistState @SerialName("status") val status: Int? = 0,
    @PersistState @SerialName("storeId") val storeId: Int? = 0,
    @PersistState @SerialName("tax") val tax: Double? = 0.0,
    @PersistState @SerialName("totalDiscount") val totalDiscount: Double? = 0.0,
    @PersistState @SerialName("totalExtraPrice") val totalExtraPrice: Double? = 0.0,
    @PersistState @SerialName("totalOptionPrice") val totalOptionPrice: Double? = 0.0,
    @PersistState @SerialName("totalPrice") val totalPrice: Double? = 0.0,
    @PersistState @SerialName("trackingUrl") val trackingUrl: String? = null,
    @PersistState @SerialName("acceptedDate") val acceptedDate: String? = null,
    @PersistState val countMinutes: Int = 0,
) : Parcelable{
    fun getCart(): List<Cart>{
        return cartJson?.let {
            val json = Json {
                prettyPrint = true
                isLenient = true
                useAlternativeNames = true
                ignoreUnknownKeys = true
                encodeDefaults = true
                explicitNulls = false
            }
            json.decodeFromString(it)
        }?: emptyList()
    }
}