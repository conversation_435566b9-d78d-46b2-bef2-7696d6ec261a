package com.thedasagroup.suminative.ui.payment

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.my_guava.failure.GuavaFailResponse
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp
import kotlinx.coroutines.launch
import kotlin.collections.forEach
import kotlin.collections.isNullOrEmpty
import kotlin.let

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PaymentActivityScreen(
    order: Order,
    onDismiss: () -> Unit,
    onPaymentSuccess: (Order2) -> Unit,
    viewModel: PaymentViewModel,
    onMakePaymentClickFail: (GuavaFailResponse) -> Unit,
    onPaymentCancelled: () -> Unit
) {
    // Initialize the order in the viewModel
    LaunchedEffect(order) {
        viewModel.updateOrder(order)
        viewModel.getTerminalList()
    }

    val coroutineScope = rememberCoroutineScope()

    // Collect view model states
    val cancelingPayment by viewModel.collectAsState(PaymentState::cancelingPayment)
    val terminalListResponse by viewModel.collectAsState(PaymentState::terminalListResponse)
    val selectedTerminal by viewModel.collectAsState(PaymentState::selectedTerminal)
    val makePaymentResponse by viewModel.collectAsState(PaymentState::makePaymentResponse)
    val checkStatusLoopResponse by viewModel.collectAsState(PaymentState::checkStatusLoopResponse)
    val paymentOrderResponse by viewModel.collectAsState(PaymentState::paymentOrderResponse)

    // Handle checkStatusLoopResponse changes
    LaunchedEffect(checkStatusLoopResponse) {
        when (checkStatusLoopResponse) {
            is Success -> {
                onPaymentSuccess(paymentOrderResponse()?.order ?: Order2())
            }
            is Fail -> {
                onMakePaymentClickFail(
                    GuavaFailResponse(
                        error = (checkStatusLoopResponse as Fail<Unit>).error.message ?: "Payment verification failed"
                    )
                )
            }
            else -> { /* Do nothing for Loading or Uninitialized */ }
        }
    }
    
    // Monitor payment cancellation
    LaunchedEffect(cancelingPayment) {
        when (cancelingPayment) {
            is Success -> {
                onPaymentCancelled()
            }
            is Fail -> {
                onMakePaymentClickFail(
                    GuavaFailResponse(
                        error = (cancelingPayment as Fail<Unit>).error.message ?: "Failed to cancel payment"
                    )
                )
            }
            else -> { /* Do nothing for Loading or Uninitialized */ }
        }
    }

    // Green theme background
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // Top bar with logo and close button
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(0.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // DASA Logo
                val headerImage = viewModel.prefs.loginResponse?.businesses?.headerImage
                val imageUrl = "${BASE_DOMAIN}/dasa/streamer?name=${headerImage}"
                val request: ImageRequest =
                    ImageRequest.Builder(LocalContext.current.applicationContext).data(imageUrl)
                        .crossfade(true).diskCacheKey(imageUrl).diskCachePolicy(CachePolicy.ENABLED)
                        .setHeader("Cache-Control", "max-age=31536000").build()
                
                AsyncImage(
                    modifier = Modifier.size(50.sdp), 
                    model = request, 
                    contentDescription = "DASA Logo"
                )

                // Close button
//                IconButton(
//                    onClick = {
//                        viewModel.cancelPayment(null)
//                        onDismiss()
//                    },
//                    modifier = Modifier.size(40.dp)
//                ) {
//                    Icon(
//                        imageVector = Icons.Default.Close,
//                        contentDescription = "Close",
//                        tint = Color.White
//                    )
//                }
            }
        }

        // Main content area
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
                .padding(16.dp)
        ) {
            // Amount to pay card
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFF1B5E20))
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "£ ${order.totalPrice?.transformDecimal() ?: "0.00"}",
                        fontSize = 32.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Amount to pay",
                        fontSize = 16.sp,
                        color = Color.White.copy(alpha = 0.8f)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Please select a payment terminal",
                        fontSize = 14.sp,
                        color = Color.White.copy(alpha = 0.7f)
                    )
                }
            }

            // Payment terminal selection section
            when {
                terminalListResponse is Loading -> {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.Center),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFF1B5E20))
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(24.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator(
                                color = Color.White
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Loading terminals...",
                                fontSize = 16.sp,
                                color = Color.White
                            )
                        }
                    }
                }
                terminalListResponse is Success && !terminalListResponse()?.data?.list.isNullOrEmpty() -> {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 8.dp)
                            .align(Alignment.Center),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFF1B5E20))
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(24.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "Select payment",
                                fontSize = 20.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color.White
                            )
                            Text(
                                text = "terminal",
                                fontSize = 20.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color.White
                            )
                            Spacer(modifier = Modifier.height(16.dp))

                            // Terminal selection cards
                            terminalListResponse()?.data?.list?.forEach { terminal ->
                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp)
                                        .clickable {
                                            viewModel.selectTerminal(terminal)
                                        },
                                    shape = RoundedCornerShape(8.dp),
                                    colors = CardDefaults.cardColors(
                                        containerColor = if (selectedTerminal?.id == terminal.id) 
                                            Color(0xFF2E7D32) else Color(0xFF1B5E20)
                                    )
                                ) {
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(16.dp),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Column {
                                            Text(
                                                text = terminal.description ?: "Terminal",
                                                fontSize = 16.sp,
                                                fontWeight = FontWeight.Medium,
                                                color = Color.White
                                            )
                                            Text(
                                                text = "ID: ${terminal.id}",
                                                fontSize = 14.sp,
                                                color = Color.White.copy(alpha = 0.7f)
                                            )
                                            Text(
                                                text = "Serial: ${terminal.serialNumber}",
                                                fontSize = 14.sp,
                                                color = Color.White.copy(alpha = 0.7f)
                                            )
                                            if (selectedTerminal?.id == terminal.id) {
                                                Text(
                                                    text = "Selected",
                                                    fontSize = 12.sp,
                                                    fontWeight = FontWeight.Bold,
                                                    color = Color.Green
                                                )
                                            }
                                        }
                                        
                                        // Checkmark icon for selected terminal
                                        if (selectedTerminal?.id == terminal.id) {
                                            Icon(
                                                imageVector = Icons.Default.Close, // Replace with checkmark when available
                                                contentDescription = "Selected",
                                                tint = Color.Green,
                                                modifier = Modifier.size(24.dp)
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else -> {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.Center),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFF1B5E20))
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(24.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "No terminals available",
                                fontSize = 16.sp,
                                color = Color.White
                            )
                        }
                    }
                }
            }

            // Payment actions
            when {
                makePaymentResponse is Loading || checkStatusLoopResponse is Loading -> {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomCenter),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFF1B5E20))
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(20.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator(
                                color = Color.White
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Processing payment...",
                                fontSize = 16.sp,
                                color = Color.White,
                                textAlign = TextAlign.Center
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            // Cancel button
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        viewModel.cancelPayment(makePaymentResponse()?.id)
                                    },
                                shape = RoundedCornerShape(8.dp),
                                colors = CardDefaults.cardColors(containerColor = Color(0xFFD32F2F))
                            ) {
                                Text(
                                    text = "Cancel Payment",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color.White,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                }
                selectedTerminal != null -> {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomCenter)
                            .clickable {
                                selectedTerminal?.let { terminal ->
                                    coroutineScope.launch {
                                        viewModel.makePayment(order = order, terminal)
                                    }
                                }
                            },
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFF1B5E20))
                    ) {
                        Text(
                            text = "Proceed to payment",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.White,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(20.dp),
                            textAlign = TextAlign.Center
                        )
                    }
                }
                else -> {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomCenter),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFF616161))
                    ) {
                        Text(
                            text = "Select a terminal to proceed",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.White.copy(alpha = 0.7f),
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(20.dp),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
    }
} 