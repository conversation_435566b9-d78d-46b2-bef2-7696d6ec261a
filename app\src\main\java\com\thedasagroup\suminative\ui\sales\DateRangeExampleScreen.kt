package com.thedasagroup.suminative.ui.sales

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.thedasagroup.suminative.ui.common.customComposableViews.CustomDateRangePicker
import com.thedasagroup.suminative.ui.common.customComposableViews.DateRange
import com.thedasagroup.suminative.ui.common.customComposableViews.DateRangeDropdown
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DateRangeExampleScreen(
    viewModel: ProductsScreenViewModel,
    onBackClick: () -> Unit
) {
    var selectedDateRange by remember { mutableStateOf(DateRange.Today) }
    var startDate by remember { mutableStateOf("") }
    var endDate by remember { mutableStateOf("") }
    var showCustomDatePicker by remember { mutableStateOf(false) }
    
    if (showCustomDatePicker) {
        CustomDateRangePicker(
            onDateRangeSelected = { start, end ->
                startDate = start
                endDate = end
                selectedDateRange = DateRange.CustomRange
                showCustomDatePicker = false
            },
            onDismiss = {
                showCustomDatePicker = false
            },
            viewModel = viewModel
        )
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Date Range Example") }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Date Range Selection Demo",
                style = MaterialTheme.typography.headlineMedium
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // Date range dropdown
                DateRangeDropdown(
                    selectedRange = selectedDateRange,
                    onRangeSelected = { start, end, range ->
                        startDate = start
                        endDate = end
                        selectedDateRange = range
                    },
                    viewModel = viewModel
                )
                
                // Custom date range button
                TextButton(
                    onClick = { showCustomDatePicker = true },
                    modifier = Modifier.background(Color(0xFF009551))
                ) {
                    Text("Pick Custom Dates", color = Color.White)
                }
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Display selected dates
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Selected Date Range",
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row {
                        Text("Range Type: ")
                        Text(
                            text = selectedDateRange.name,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row {
                        Text("Start Date: ")
                        Text(
                            text = startDate,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row {
                        Text("End Date: ")
                        Text(
                            text = endDate,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }
    }
} 