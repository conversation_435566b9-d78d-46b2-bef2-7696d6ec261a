package com.thedasagroup.suminative.data.api

import android.util.Log
import com.pluto.plugins.network.ktor.PlutoKtorInterceptor
import io.ktor.client.HttpClient
import io.ktor.client.engine.android.Android
import io.ktor.client.plugins.DefaultRequest
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logger
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.plugins.observer.ResponseObserver
import io.ktor.client.request.accept
import io.ktor.client.request.header
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.contentType
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import com.thedasagroup.suminative.BuildConfig
import kotlinx.serialization.ExperimentalSerializationApi
import timber.log.Timber

@OptIn(ExperimentalSerializationApi::class)
val apiClient = HttpClient(Android) {

    install(ContentNegotiation) {
        json(
            Json {
                prettyPrint = true
                isLenient = true
                useAlternativeNames = true
                ignoreUnknownKeys = true
                encodeDefaults = true
                explicitNulls = false
            }
        )
    }

    install(PlutoKtorInterceptor)

    install(HttpTimeout) {
        requestTimeoutMillis = 3 * 60000
        connectTimeoutMillis = 3 * 60000
        socketTimeoutMillis = 3 * 60000
    }

    install(Logging) {
        logger = object : Logger {
            override fun log(message: String) {
                runCatching {
                    if(BuildConfig.DEBUG){
                        Log.v("Logger Ktor =>", message)
                    }
                    Timber.tag("Logger Ktor =>").v(message)
                }
            }
        }
        level = LogLevel.ALL
    }

    install(ResponseObserver) {
        onResponse { response ->
            runCatching{
                if(BuildConfig.DEBUG) {
                    Log.d("HTTP status:", "${response.status.value}")
                }
                Timber.tag("HTTP status:").v("${response.status.value}")
            }
        }
    }

    install(DefaultRequest) {
        header(HttpHeaders.ContentType, ContentType.Application.Json)
    }

    defaultRequest {
        contentType(ContentType.Application.Json)
        accept(ContentType.Application.Json)
    }
}

val DOMAIN_ONLY = BuildConfig.DOMAIN_NAME
//val DOMAIN_ONLY = "thedasagroup.com"
val BASE_DOMAIN ="https://${DOMAIN_ONLY}"
val BASE_URL = "${BASE_DOMAIN}/dasa/v1/api/requestHandler"
val SOCKET = "wss://${DOMAIN_ONLY}/BackendDASA-1.0.0/ws"
val GET_PENDING_ORDERS = "${BASE_DOMAIN}/BackendDASA-1.0.0/store/getPendingOrders"
val GET_ALL_ORDERS = "${BASE_DOMAIN}/BackendDASA-1.0.0/store/getAllOrders"
val GET_SCHEDULED_ORDERS = "${BASE_DOMAIN}/BackendDASA-1.0.0/store/getScheduledItems"
val GET_STOCK_ITEMS = "${BASE_DOMAIN}/BackendDASA-1.0.0/store/getAllItems"
val EDIT_STOCK = "${BASE_DOMAIN}/BackendDASA-1.0.0/store/editStock"
val LOGIN = "${BASE_DOMAIN}/BackendDASA-1.0.0/login/store"
val PLACE_ORDER = "${BASE_DOMAIN}/BackendDASA-1.0.0/createNewPOSOrder"
val CLOUD_PRINT = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/printer/print"
val SALES = "${BASE_DOMAIN}/BackendDASA-1.0.0/getDateWiseSales"
val GET_POS_SETTINGS = "${BASE_DOMAIN}/BackendDASA-1.0.0/getPOSSettings"
val GET_SALES_REPORT = "${BASE_DOMAIN}/BackendDASA-1.0.0/getSalesReport"
val GET_CATEGORY_SORTING = "${BASE_DOMAIN}/BackendDASA-1.0.0/getCategorySorting"
val GET_STORE_CONFIGURATIONS = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/storeConfigurations/get"

val UPLOAD_FILES_URL = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/files/upload"
val STORE_USER_LOGIN = "${BASE_DOMAIN}/BackendDASA-1.0.0/login/storeUserLogin"
val CLOCK_IN_USER_TIME = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/users/clockInUserTime"
val CLOCK_OUT_USER_TIME = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/users/clockOutUserTime"

// Reservations API endpoints
val GET_ACTIVE_RESERVATIONS = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/reservations/active"
val GET_ALL_RESERVATIONS = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/reservations/all"
val CREATE_RESERVATION = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/reservations"
val EDIT_RESERVATION = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/reservations"
val CANCEL_RESERVATION = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/reservations"
val GET_RESERVATION_AREAS = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/reservations/areas"
val GET_RESERVATION_TABLES = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/reservations/tables"