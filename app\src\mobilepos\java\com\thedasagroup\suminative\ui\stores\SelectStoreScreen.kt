package com.thedasagroup.suminative.ui.stores

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.Gray
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.model.response.login.Store
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.ui.login.LoginScreenViewModel
import ir.kaaveh.sdpcompose.sdp

@Composable
fun SelectStoreScreen(prefs: Prefs, onStoreSelect: (Store) -> Unit) {
    val stores = prefs.loginResponse?.stores ?: mutableListOf()
    LazyVerticalGrid(
        modifier = Modifier.padding(10.dp),
        columns = GridCells.Fixed(2),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(stores) { store ->
            StoreCard(store = store, onStoreSelect = onStoreSelect)
        }
    }
}

@Composable
fun StoreCard(store: Store, onStoreSelect: (Store) -> Unit) {
    Column(
        modifier = Modifier.width(120.dp).height(220.sdp), horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AsyncImage(
            model = "${BASE_DOMAIN}/dasa/streamer?name=${store.banner}",
            contentDescription = "",
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .size(64.dp)
                .clip(CircleShape)
                .border(2.dp, Gray, CircleShape)
        )
        Spacer(modifier = Modifier.size(8.dp))
        Text(
            text =  store.name ?: "",
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            textAlign = TextAlign.Center, // make text center horizontal
            modifier = Modifier
                .width(120.dp)
                .wrapContentHeight() // make text center vertical
        )
        Spacer(modifier = Modifier.size(8.dp))
        Text(
            text = store.address ?: "",
            maxLines = 3,
            overflow = TextOverflow.Ellipsis,
            textAlign = TextAlign.Center, // make text center horizontal
            modifier = Modifier
                .width(120.dp)
                .wrapContentHeight() // make text center vertical
        )
        Spacer(modifier = Modifier.size(8.dp))
        TextButton(
            onClick = {
                onStoreSelect(store)
            }, modifier = Modifier
                .background(color = Color(0xFF25F6AF))
                .width(120.dp)
        ) {
            Text(
                text = "CHOOSE STORE",
                color = Color.White,
                textAlign = TextAlign.Center, // make text center horizontal
                modifier = Modifier
                    .width(120.dp)
                    .wrapContentHeight() // make text center vertical
            )
        }
    }
}