package com.thedasagroup.suminative.ui.categories

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.response.category_sorting.CategorySortingResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class CategoriesViewModel @AssistedInject constructor(
    @Assisted state: CategoriesState,
    val prefs: Prefs,
    val getCategoriesUseCase: GetCategoriesUseCase
) : MavericksViewModel<CategoriesState>(state) {

    suspend fun getCategories(): StateFlow<Async<CategorySortingResponse>> {
        val flow = MutableStateFlow<Async<CategorySortingResponse>>(Loading())
        setState {
            copy(categoriesResponse = Loading())
        }
        getCategoriesUseCase().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(categoriesResponse = it())
                }
                else -> {
                    flow.value = Uninitialized
                    copy(categoriesResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<CategoriesViewModel, CategoriesState>

    companion object : MavericksViewModelFactory<CategoriesViewModel, CategoriesState> by hiltMavericksViewModelFactory()
}

data class CategoriesState(
    val categoriesResponse: Async<CategorySortingResponse> = Uninitialized
) : MavericksState 