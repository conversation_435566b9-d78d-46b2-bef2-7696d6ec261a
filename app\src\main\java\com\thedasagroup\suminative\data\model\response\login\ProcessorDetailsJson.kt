package com.thedasagroup.suminative.data.model.response.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ProcessorDetailsJson(
    @SerialName("client_name")
    val clientName: String? = null,
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("description")
    val description: String? = null,
    @SerialName("id")
    val id: String? = null,
    @SerialName("key")
    val key: String? = null,
    @SerialName("merchant")
    val merchant: Merchant? = null,
    @SerialName("status")
    val status: String? = null,
    @SerialName("updated_at")
    val updatedAt: String? = null,
)