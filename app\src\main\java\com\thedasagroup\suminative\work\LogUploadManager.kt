package com.thedasagroup.suminative.work

import android.content.Context
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import timber.log.Timber
import java.util.concurrent.TimeUnit

/**
 * Manages scheduling log uploads to Firebase Storage
 */
object LogUploadManager {

    private const val UPLOAD_LOGS_WORK_NAME = "upload_logs_work"

    /**
     * Schedule periodic uploads of logs to Firebase Storage
     */
    fun schedulePeriodicLogUploads(context: Context) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .setRequiresBatteryNotLow(true)
            .build()

        val workRequest = PeriodicWorkRequestBuilder<UploadLogsWorker>(
            repeatInterval = 15,
            repeatIntervalTimeUnit = TimeUnit.MINUTES
        )
            .setConstraints(constraints)
            .build()

        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            UPLOAD_LOGS_WORK_NAME,
            ExistingPeriodicWorkPolicy.UPDATE,
            workRequest
        )

        Timber.i("Periodic log uploads scheduled")
    }

    /**
     * Upload logs now (without waiting for the periodic schedule)
     */
    fun uploadLogsNow(context: Context) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val workRequest = OneTimeWorkRequestBuilder<UploadLogsWorker>()
            .setConstraints(constraints)
            .build()

        WorkManager.getInstance(context).enqueueUniqueWork(
            "upload_logs_now",
            ExistingWorkPolicy.REPLACE,
            workRequest
        )

        Timber.i("Immediate log upload requested")
    }

    /**
     * Cancel all scheduled log uploads
     */
    fun cancelLogUploads(context: Context) {
        WorkManager.getInstance(context).cancelUniqueWork(UPLOAD_LOGS_WORK_NAME)
        Timber.i("Log uploads canceled")
    }
} 