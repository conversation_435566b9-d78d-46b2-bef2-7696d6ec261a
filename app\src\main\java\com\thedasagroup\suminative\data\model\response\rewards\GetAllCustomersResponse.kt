package com.thedasagroup.suminative.data.model.response.rewards

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GetAllCustomersResponse(
    @SerialName("success")
    val success: Boolean? = null,
    @SerialName("message")
    val message: String? = null,
    @SerialName("data")
    val data: List<RewardsCustomer>? = null,
    @SerialName("statusCode")
    val statusCode: Int? = null
)
