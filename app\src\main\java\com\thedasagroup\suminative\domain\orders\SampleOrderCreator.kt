package com.thedasagroup.suminative.domain.orders

import com.thedasagroup.suminative.data.database.LocalOrder
import com.thedasagroup.suminative.data.database.LocalOrderItem
import com.thedasagroup.suminative.data.database.LocalOrderRepository
import java.util.*
import javax.inject.Inject

class SampleOrderCreator @Inject constructor(
    private val orderRepository: LocalOrderRepository
) {
    
    suspend fun createSampleOrders() {
        val sampleOrders = listOf(
            createSampleOrder(
                orderId = "ORD-001-${UUID.randomUUID().toString().take(8)}",
                customerName = "John Doe",
                customerPhone = "+1234567890",
                orderType = "DINE_IN",
                status = "PENDING",
                total = 25.99,
                tableNumber = 5,
                paymentStatus = "PENDING",
                synced = false // Not synced yet
            ),
            createSampleOrder(
                orderId = "ORD-002-${UUID.randomUUID().toString().take(8)}",
                customerName = "<PERSON>",
                customerPhone = "+1987654321",
                orderType = "TAKEAWAY",
                status = "ACCEPTED",
                total = 18.50,
                paymentStatus = "PAID",
                synced = true // Already synced
            ),
            createSampleOrder(
                orderId = "ORD-003-${UUID.randomUUID().toString().take(8)}",
                customerName = "Bob Wilson",
                orderType = "DELIVERY",
                status = "PREPARING",
                total = 32.75,
                deliveryAddress = "123 Main St, City",
                paymentStatus = "PAID",
                synced = false // Needs sync
            ),
            createSampleOrder(
                orderId = "ORD-004-${UUID.randomUUID().toString().take(8)}",
                customerName = "Alice Johnson",
                orderType = "DINE_IN",
                status = "READY",
                total = 42.00,
                tableNumber = 12,
                paymentStatus = "PAID",
                synced = true // Synced
            ),
            createSampleOrder(
                orderId = "ORD-005-${UUID.randomUUID().toString().take(8)}",
                customerName = "Mike Brown",
                orderType = "TAKEAWAY",
                status = "COMPLETED",
                total = 15.25,
                paymentStatus = "PAID",
                synced = false // Completed but not synced
            )
        )
        
        sampleOrders.forEach { order ->
            orderRepository.insertOrder(order)
        }
        
        // Add some sample items for the first order
        val sampleItems = listOf(
            LocalOrderItem(
                orderId = sampleOrders[0].orderId,
                itemId = 1,
                itemName = "Cheeseburger",
                itemDescription = "Beef patty with cheese, lettuce, tomato",
                quantity = 2,
                unitPrice = 8.99,
                totalPrice = 17.98,
                category = "Burgers"
            ),
            LocalOrderItem(
                orderId = sampleOrders[0].orderId,
                itemId = 2,
                itemName = "French Fries",
                itemDescription = "Crispy golden fries",
                quantity = 1,
                unitPrice = 4.99,
                totalPrice = 4.99,
                category = "Sides"
            ),
            LocalOrderItem(
                orderId = sampleOrders[0].orderId,
                itemId = 3,
                itemName = "Coca Cola",
                itemDescription = "Regular Coke",
                quantity = 1,
                unitPrice = 2.99,
                totalPrice = 2.99,
                category = "Beverages"
            )
        )
        
        sampleItems.forEach { item ->
            orderRepository.insertOrderItem(item)
        }
    }
    
    private fun createSampleOrder(
        orderId: String,
        customerName: String,
        customerPhone: String? = null,
        orderType: String,
        status: String,
        total: Double,
        tableNumber: Long? = null,
        deliveryAddress: String? = null,
        paymentStatus: String = "PENDING",
        synced: Boolean = false
    ): LocalOrder {
        val now = System.currentTimeMillis()
        return LocalOrder(
            orderId = orderId,
            customerId = null,
            customerName = customerName,
            customerPhone = customerPhone,
            orderType = orderType,
            status = status,
            subtotal = total * 0.9, // 90% of total as subtotal
            tax = total * 0.1, // 10% tax
            discount = 0.0,
            total = total,
            paymentMethod = if (paymentStatus == "PAID") "CASH" else null,
            paymentStatus = paymentStatus,
            notes = "Sample order for testing",
            tableNumber = tableNumber,
            deliveryAddress = deliveryAddress,
            createdAt = now - (Math.random() * 86400000).toLong(), // Random time within last 24 hours
            updatedAt = now,
            storeId = 1L,
            waiterId = 1L,
            waiterName = "Test Waiter",
            synced = synced
        )
    }
} 