package com.thedasagroup.suminative.ui.stock

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.model.request.stock.ChangeStockRequest
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.ProductRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.domain.categories.SyncCategoriesUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class ChangeStockUseCase(
    private val stockRepository: StockRepository,
    private val prefs: Prefs,
    private val productRepository: ProductRepository,
    private val syncCategoriesUseCase: SyncCategoriesUseCase
) : StockUseCase(stockRepository, prefs, syncCategoriesUseCase = syncCategoriesUseCase) {
    suspend operator fun invoke(changeStockRequest: ChangeStockRequest) : StateFlow<Async<StockItemsResponse>> {
        val response = stockRepository.changeStock(
            request = changeStockRequest
        )
        return when (response.value) {
            is Success -> {
                // Update stock in database as well
                productRepository.updateProductStock(
                    productId = changeStockRequest.itemId,
                    stock = changeStockRequest.stock,
                )
                return super.invoke()
            }
            else -> {
                MutableStateFlow(Success(StockItemsResponse()))
            }
        }
    }
}