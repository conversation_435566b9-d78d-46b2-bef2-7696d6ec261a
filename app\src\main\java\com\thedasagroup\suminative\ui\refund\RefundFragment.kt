package com.thedasagroup.suminative.ui.refund

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.DialogFragment
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.mocking.EmptyMocks.mocks
import com.airbnb.mvrx.mocking.MockableMavericksView
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import com.thedasagroup.suminative.ui.payment.PaymentScreen
import com.thedasagroup.suminative.ui.payment.PaymentViewModel
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme

class RefundFragment : DialogFragment(), MavericksView {

    val paymentViewModel : PaymentViewModel by fragmentViewModel()
    private var onPaymentSuccess : ((Order2) -> Unit) = {}
    private lateinit var guavaOrder: GuavaOrder

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }
    
    override fun onStart() {
        super.onStart()
        guavaOrder = arguments?.getParcelable(ARG_ORDER) ?: GuavaOrder()
        dialog?.let { dialog ->
            // Make dialog fullscreen with proper width/height
            dialog.window?.let { window ->
                window.setLayout(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.MATCH_PARENT
                )
                // Optional: remove background dimming
                window.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            }
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                SumiNativeTheme {
                    androidx.compose.material3.Surface(
                        modifier = Modifier.fillMaxSize(fraction = 0.7f),
                        color = Color(0xFF0A2540)
                    ) {
                        RefundScreen(
                            onMakePaymentClickSuccess = {order1 ->
                                requireActivity().let { activity ->
                                    AlertDialog.Builder(activity)
                                        .setTitle("Success")
                                        .setMessage("Payment completed successfully")
                                        .setPositiveButton("OK") { dialog, _ ->
                                            onPaymentSuccess(order1)
                                            dismiss() // Dismiss the dialog fragment
                                        }
                                        .show()
                                }
                            },
                            onMakePaymentClickFail = {guavaFailResponse ->
                                requireActivity().let { activity ->
                                    AlertDialog.Builder(activity)
                                        .setTitle("Error")
                                        .setMessage(guavaFailResponse.error)
                                        .setPositiveButton("OK") { dialog, _ ->
                                            dialog.dismiss()
                                        }
                                        .show()
                                }
                            },
                            onPaymentCancelled = {
                                requireActivity().let { activity ->
                                    AlertDialog.Builder(activity)
                                        .setTitle("Payment Cancelled")
                                        .setMessage("The payment has been cancelled.")
                                        .setPositiveButton("OK") { dialog, _ ->
                                            dialog.dismiss()
                                            <EMAIL>()
                                        }
                                        .show()
                                }
                            },
                            paymentViewModel = paymentViewModel,
                            guavaOrder = guavaOrder
                        )
                    }
                }
            }
        }
    }

    override fun invalidate() {
        // Called when the state is updated
    }
    
    companion object {
        // Tag for fragment manager
        const val TAG = "PaymentDialogFragment"
        
        // Argument keys
        const val ARG_ORDER = "order"
        
        fun newInstance(guavaOrder: GuavaOrder, onPaymentSuccess : (Order2) -> Unit): RefundFragment {
            val fragment = RefundFragment()
            return fragment.apply {
                this.onPaymentSuccess = onPaymentSuccess
                fragment.arguments = Bundle().apply {
                    putParcelable(ARG_ORDER, guavaOrder)
                }
            }
        }
    }
} 