# Create Reservation API Implementation

## Overview
Successfully implemented the create/update reservation API using <PERSON><PERSON> in the ReservationsRepository.kt file, created the necessary use cases, and integrated them with the reservation view model.

## API Implementation

### Endpoint
- **Method**: POST
- **URL**: `https://dasadirect.com/BackendDASA-1.0.0/api/reservations`
- **Content-Type**: `application/json`

### Request Structure
```json
{
    "id": null, // null for new, or the existing ID to update
    "storeId": 158,
    "tableId": 12,
    "customerId": 321,
    "guestName": "Jane Doe",
    "guestPhone": "03001234567",
    "numPeople": 4,
    "reservationStatus": 0, // e.g. 0 = pending, 1 = confirmed, etc.
    "reservationTime": "2025-07-22T18:30",
    "timezoneOffset": 300 // minutes east of UTC (e.g. +5h = 300)
}
```

### Response Structure
```json
{
    "id": 30,
    "storeId": 158,
    "tableId": 12,
    "customerId": 321,
    "reservationStatus": 0,
    "guestName": null,
    "guestPhone": null,
    "reservationTime": "2025-07-22T23:30:00",
    "numPeople": 4,
    "createdAt": "2025-07-21T16:57:12.17"
}
```

## Files Created/Modified

### 1. Data Models

#### CreateReservationRequest.kt
- Added `CreateReservationRequest` data class to handle the new API request structure
- Located in: `app/src/main/java/com/thedasagroup/suminative/data/model/request/reservations/EditReservationRequest.kt`

#### CreateReservationResponse.kt
- Added `CreateReservationResponse` data class to handle the API response
- Located in: `app/src/main/java/com/thedasagroup/suminative/data/model/response/reservations/ReservationsResponse.kt`

### 2. Repository Layer

#### ReservationsRepository.kt
- Added `createReservation()` method using Ktor HTTP client
- Handles both create (id = null) and update (id = existing ID) operations
- Returns `StateFlow<Async<CreateReservationResponse>>`
- Added necessary imports for Ktor POST and response handling

### 3. Use Case Layer

#### CreateReservationUseCase.kt
- New use case that wraps the repository call
- Follows the existing pattern used by other reservation use cases
- Located in: `app/src/main/java/com/thedasagroup/suminative/ui/reservations/CreateReservationUseCase.kt`

### 4. View Model Layer

#### ReservationsViewModel.kt
- Added `createReservation()` method
- Integrated with the new use case
- Automatically refreshes reservation lists after successful creation
- Handles loading states and error handling

#### ReservationsState.kt
- Added `createReservationResponse: Async<CreateReservationResponse>` to track creation state
- Maintains consistency with existing state structure

### 5. Dependency Injection

#### AppUseCaseModule.kt
- Added provider for `CreateReservationUseCase`
- Properly configured with Singleton scope
- Added necessary import

#### ApiClient.kt
- Added `CREATE_RESERVATION` endpoint constant
- Follows existing naming convention

## Usage Examples

### Creating a New Reservation
```kotlin
val createRequest = CreateReservationRequest(
    id = null, // null for new reservation
    storeId = 158,
    tableId = 12,
    customerId = 321,
    guestName = "Jane Doe",
    guestPhone = "03001234567",
    numPeople = 4,
    reservationStatus = 0, // 0 = pending
    reservationTime = "2025-07-22T18:30",
    timezoneOffset = 300
)

// In ViewModel
viewModel.createReservation(createRequest)
```

### Updating an Existing Reservation
```kotlin
val updateRequest = CreateReservationRequest(
    id = 30, // existing reservation ID
    storeId = 158,
    tableId = 15, // changed table
    customerId = 321,
    guestName = "Jane Doe Updated",
    guestPhone = "03001234567",
    numPeople = 6, // changed party size
    reservationStatus = 1, // changed to confirmed
    reservationTime = "2025-07-22T19:00", // changed time
    timezoneOffset = 300
)

// In ViewModel
viewModel.createReservation(updateRequest)
```

### Observing State in UI
```kotlin
// In Fragment/Activity
viewModel.withState { state ->
    when (val response = state.createReservationResponse) {
        is Success -> {
            val reservation = response()
            // Handle successful creation/update
            showSuccess("Reservation ${if (reservation.id != null) "updated" else "created"} successfully")
        }
        is Fail -> {
            // Handle error
            showError("Failed to create reservation: ${response.error.message}")
        }
        is Loading -> {
            // Show loading indicator
            showLoading()
        }
        else -> {
            // Initial state
        }
    }
}
```

## Key Features

1. **Unified API**: Single endpoint handles both create and update operations based on the `id` field
2. **Ktor Integration**: Uses the existing Ktor HTTP client for consistency
3. **Async State Management**: Leverages MvRx Async for proper state handling
4. **Auto-refresh**: Automatically refreshes reservation lists after successful operations
5. **Error Handling**: Proper error handling through the existing BaseRepository pattern
6. **Dependency Injection**: Fully integrated with Hilt DI system
7. **Type Safety**: Strongly typed request/response models with Kotlinx Serialization

## Testing

The implementation includes comprehensive usage examples in `ReservationsRepositoryUsageExample.kt` that demonstrate:
- Creating new reservations
- Updating existing reservations
- Error handling patterns
- State observation patterns

All components follow the existing architectural patterns and are ready for integration with the UI layer.
