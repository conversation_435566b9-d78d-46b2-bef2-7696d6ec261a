package com.thedasagroup.suminative.domain.categories

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.database.CategoryRepository
import com.thedasagroup.suminative.data.model.request.category_sorting.CategorySortingRequest
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.StockRepository
import javax.inject.Inject

class SyncCategoriesUseCase @Inject constructor(
    private val stockRepository: StockRepository,
    private val categoryRepository: CategoryRepository,
    private val prefs: Prefs
) {
    
    suspend operator fun invoke(forceSync: Boolean = false): Result<Int> {
        return try {
            val storeId = prefs.store?.id ?: return Result.failure(Exception("Store ID not found"))
            
            // Check if categories exist in database and if force sync is not requested
            if (!forceSync && categoryRepository.hasCategoriesForStore(storeId = storeId.toLong())) {
                return Result.success(0) // No sync needed
            }
            
            // Get category sorting from API
            val categorySortingResult = stockRepository.getCategorySorting(
                CategorySortingRequest(storeId = storeId.toString())
            )
            
            when (categorySortingResult.value) {
                is Success -> {
                    val categories = categorySortingResult.value()?.categories
                    if (categories.isNullOrEmpty()) {
                        return Result.failure(Exception("No categories found in API response"))
                    }
                    
                    // Sync categories to database
                    categoryRepository.syncCategoriesFromApi(categories, storeId = storeId.toLong())
                    Result.success(categories.size)
                }
                else -> {
                    Result.failure(Exception("Failed to fetch categories from API"))
                }
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Get categories from database
    suspend fun getCategoriesFromDatabase(storeId: Long): List<String> {
        return categoryRepository.getCategoryNamesSorted(storeId)
    }
    
    // Check if categories exist in database
    suspend fun hasCategoriesInDatabase(storeId: Long): Boolean {
        return categoryRepository.hasCategoriesForStore(storeId)
    }
} 