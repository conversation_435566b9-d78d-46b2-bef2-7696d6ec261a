package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.rewards.AddPointsRequest
import com.thedasagroup.suminative.data.model.request.rewards.GetAllCustomersRequest
import com.thedasagroup.suminative.data.model.response.rewards.AddPointsResponse
import com.thedasagroup.suminative.data.model.response.rewards.GetAllCustomersResponse
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class RewardsRepository : BaseRepository() {
    
    suspend fun getAllCustomers(request: GetAllCustomersRequest): StateFlow<Async<GetAllCustomersResponse>> {
        val flow = MutableStateFlow<Async<GetAllCustomersResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val apiResponse = apiClient.get(urlString = "$BASE_DOMAIN/BackendDASA-1.0.0/api/customer/getAllCustomers") {
                    parameter("customerId", request.customerId)
                    parameter("businessId", request.businessId)
                }.body<GetAllCustomersResponse>()
                return@safeApiCall Success(apiResponse)
            }
            flow.value = response
        }
        return flow
    }
    
    suspend fun addPoints(request: AddPointsRequest): StateFlow<Async<AddPointsResponse>> {
        val flow = MutableStateFlow<Async<AddPointsResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val apiResponse = apiClient.post(urlString = "$BASE_DOMAIN/BackendDASA-1.0.0/api/rewards/addPoints") {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }.body<AddPointsResponse>()
                return@safeApiCall Success(apiResponse)
            }
            flow.value = response
        }
        return flow
    }
}
