package com.thedasagroup.suminative.ui.payment

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.thedasagroup.suminative.data.model.request.order.Order
import dagger.hilt.android.AndroidEntryPoint

/**
 * Activity to handle payment processing.
 * This is now a lightweight activity that shows the payment dialog.
 * This approach allows payment UI to be shown either as a dialog from other screens
 * or as a standalone activity for deeper integration.
 */
@AndroidEntryPoint
class PaymentActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (savedInstanceState == null) {

            // Show the payment dialog
//            PaymentDialogHelper.showPaymentDialog(this, order)
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // Ensure dialog is dismissed when activity is destroyed
        PaymentDialogHelper.dismissPaymentDialog(this)
    }

    companion object {
        private const val ARG_ORDER = "extra_order"
        const val RESULT_ORDER = "result_order"

        fun createIntent(context: Context, order: Order): Intent {
            return Intent(context, PaymentActivity::class.java).apply {
                putExtra(ARG_ORDER, order)
            }
        }
    }
}