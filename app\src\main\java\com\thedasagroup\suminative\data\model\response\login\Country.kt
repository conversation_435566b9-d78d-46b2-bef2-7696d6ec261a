package com.thedasagroup.suminative.data.model.response.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Country(
    @SerialName("code")
    val code: String? = null,
    @SerialName("currency")
    val currency: String? = null,
    @SerialName("currencySymbol")
    val currencySymbol: String? = null,
    @SerialName("id")
    val id: Int? = null,
    @SerialName("isd")
    val isd: String? = null,
    @SerialName("name")
    val name: String? = null,
)