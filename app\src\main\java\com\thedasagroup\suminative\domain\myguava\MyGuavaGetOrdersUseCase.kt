package com.thedasagroup.suminative.domain.myguava

import com.airbnb.mvrx.Async
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.my_guava.orders.CreateOrderRequest
import com.thedasagroup.suminative.data.model.request.my_guava.orders.GetListOfOrdersRequest
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse
import com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GetListOfOrdersResponse
import com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.ui.utils.formatDate
import kotlinx.coroutines.flow.StateFlow
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.UUID

open class MyGuavaGetOrdersUseCase(
    private val guavaRepository: MyGuavaRepository,
    val trueTimeImpl: TrueTimeImpl
) {
    suspend operator fun invoke(): StateFlow<Async<GetListOfOrdersResponse>> {
        val now = trueTimeImpl.now()
        val calNow = Calendar.getInstance()
        calNow.time = now
        val dateTo =  calNow.time.formatDate("yyyy-MM-dd'T'HH:mm:ssZ")
        val request = GetListOfOrdersRequest(
            size = 100,
            page = 0,
            sortBy = "createdAt",
            direction = "desc",
            dateTo = dateTo
        )
        val guavaResponse = guavaRepository.getListOfOrders2(request = request)
        return guavaResponse
    }
}
