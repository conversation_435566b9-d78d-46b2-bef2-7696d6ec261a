package com.thedasagroup.suminative.ui.service
import android.app.Notification
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.job.JobParameters
import android.app.job.JobService
import android.content.Intent
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.ui.MainActivity
import com.thedasagroup.suminative.ui.utils.ChatWebSocketClient
import dagger.hilt.android.AndroidEntryPoint
import java.net.URI
import javax.inject.Inject


@AndroidEntryPoint
class MySocketJobService : JobService() {

    @Inject
    lateinit var prefs: Prefs

    private val TAG: String? = MySocketJobService::class.java.simpleName

    private var webSocketClient: ChatWebSocketClient? = null
    private var webSocketClient2: ChatWebSocketClient? = null

    override fun onStartJob(p0: JobParameters?): Boolean {
        Log.v(TAG, "onStartJob")
        val serverUri = URI("wss://dasasplace.com/dasa/websocket")
        val serverUri2 = URI("http://3.140.187.185:9000/ws/topic/store")
        val notification = createNotification()
        val mgr =
            getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        mgr.notify(1234, notification)
        startForeground(1234, notification)

        webSocketClient = ChatWebSocketClient(serverUri) { message ->
            runCatching {
                run {
                    val parts = message.split(":")
                    Log.v(TAG, "message: ${parts}")
                    if(parts[2] == "105"){
                        sendMessage(parts[0])
                    }
                }
            }
        }
        // connect to websocket server
        webSocketClient?.connect()

        webSocketClient2 = ChatWebSocketClient(serverUri2) { message ->
            runCatching {
                run {
                    val parts = message.split(":")
                    Log.v(TAG, "message: ${parts}")
//                    if(parts[1] == prefs.loginResponse?.socketId){
                        sendMessage(parts[0])
//                    }
                }
            }
        }
        // connect to websocket server
        webSocketClient2?.connect()
        return true
    }

    override fun onStopJob(p0: JobParameters?): Boolean {
        Log.v(TAG, "onStopJob")
        val mgr =
            getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        mgr.cancel(1234)
        webSocketClient?.close()
        webSocketClient2?.close()
        return true
    }


    private fun createNotification() : Notification {
        val notificationIntent = Intent(
            this,
            MainActivity::class.java
        )

        val pendingIntent = PendingIntent.getActivity(
            this, 0,
            notificationIntent, PendingIntent.FLAG_IMMUTABLE
        )

        val mBuilder: NotificationCompat.Builder =
            NotificationCompat.Builder(this)
                .setSmallIcon(android.R.drawable.star_on)
                .setContentTitle("DasaDirect")
                .setContentText("Looking for Orders")
                .setContentIntent(pendingIntent)
                .setOngoing(true)
        return mBuilder.build()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        return START_STICKY;
    }


    private fun sendMessage(message : String) {
        Log.d("sender", "Broadcasting message")
        val intent = Intent("custom-event-name")
        // You can also include some extra data.
        intent.putExtra("message", message)
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
    }
}