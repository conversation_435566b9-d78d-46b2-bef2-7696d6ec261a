CREATE TABLE ProductEntity (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    productId INTEGER UNIQUE, -- Remote server ID
    name TEXT NOT NULL,
    description TEXT,
    additionalInfo TEXT,
    category TEXT,
    categoryId INTEGER,
    price REAL NOT NULL DEFAULT 0.0,
    billAmount REAL DEFAULT 0.0,
    tax REAL DEFAULT 0.0,
    vat INTEGER NOT NULL DEFAULT 0, -- 0 = false, 1 = true
    pic TEXT, -- Image URL/path
    stock INTEGER NOT NULL DEFAULT 0, -- 0 = off menu, 1 = in stock, 2 = sold out for today
    ingredients TEXT,
    preparationTime TEXT,
    servingSize TEXT,
    dailyCapacity INTEGER DEFAULT 0,
    discountType INTEGER DEFAULT 0,
    discountedAmount REAL DEFAULT 0.0,
    brandId INTEGER,
    businessId INTEGER,
    storeId INTEGER NOT NULL,
    unitId INTEGER,
    createdBy INTEGER,
    createdOn TEXT,
    modifiedBy INTEGER,
    modifiedOn TEXT,
    createdAt INTEGER NOT NULL, -- Local timestamp
    updatedAt INTEGER NOT NULL, -- Local timestamp
    synced INTEGER NOT NULL DEFAULT 0 -- 0 = not synced, 1 = synced
);

-- Indexes for better performance
CREATE INDEX idx_product_category ON ProductEntity(categoryId);
CREATE INDEX idx_product_store ON ProductEntity(storeId);
CREATE INDEX idx_product_stock ON ProductEntity(stock);
CREATE INDEX idx_product_name ON ProductEntity(name);
CREATE INDEX idx_product_synced ON ProductEntity(synced);
CREATE INDEX idx_product_id ON ProductEntity(productId);

-- Queries for Product operations

insertProduct:
INSERT INTO ProductEntity (
    productId, name, description, additionalInfo, category, categoryId,
    price, billAmount, tax, vat, pic, stock, ingredients, preparationTime,
    servingSize, dailyCapacity, discountType, discountedAmount, brandId,
    businessId, storeId, unitId, createdBy, createdOn, modifiedBy,
    modifiedOn, createdAt, updatedAt, synced
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

insertOrReplaceProduct:
INSERT OR REPLACE INTO ProductEntity (
    productId, name, description, additionalInfo, category, categoryId,
    price, billAmount, tax, vat, pic, stock, ingredients, preparationTime,
    servingSize, dailyCapacity, discountType, discountedAmount, brandId,
    businessId, storeId, unitId, createdBy, createdOn, modifiedBy,
    modifiedOn, createdAt, updatedAt, synced
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

getAllProducts:
SELECT * FROM ProductEntity ORDER BY name ASC;

getProductById:
SELECT * FROM ProductEntity WHERE id = ?;

getProductByProductId:
SELECT * FROM ProductEntity WHERE productId = ?;

getProductsByStore:
SELECT * FROM ProductEntity WHERE storeId = ? ORDER BY category ASC, name ASC;

getProductsByCategory:
SELECT * FROM ProductEntity WHERE categoryId = ? ORDER BY name ASC;

getProductsByCategoryAndStore:
SELECT * FROM ProductEntity WHERE categoryId = ? AND storeId = ? ORDER BY name ASC;

getInStockProducts:
SELECT * FROM ProductEntity WHERE stock = 1 ORDER BY name ASC;

getInStockProductsByStore:
SELECT * FROM ProductEntity WHERE stock = 1 AND storeId = ? ORDER BY category ASC, name ASC;

getProductsByStockStatus:
SELECT * FROM ProductEntity WHERE stock = ? ORDER BY name ASC;

searchProductsByName:
SELECT * FROM ProductEntity WHERE name LIKE '%' || ? || '%' ORDER BY name ASC;

searchProductsByNameAndStore:
SELECT * FROM ProductEntity WHERE name LIKE '%' || ? || '%' AND storeId = ? ORDER BY name ASC;

getProductCategories:
SELECT DISTINCT category, categoryId FROM ProductEntity WHERE category IS NOT NULL ORDER BY category ASC;

getProductCategoriesByStore:
SELECT DISTINCT category, categoryId FROM ProductEntity WHERE category IS NOT NULL AND storeId = ? ORDER BY category ASC;

getUnsyncedProducts:
SELECT * FROM ProductEntity WHERE synced = 0 ORDER BY createdAt ASC;

getSyncedProducts:
SELECT * FROM ProductEntity WHERE synced = 1 ORDER BY name ASC;

updateProduct:
UPDATE ProductEntity 
SET name = ?, description = ?, additionalInfo = ?, category = ?, categoryId = ?,
    price = ?, billAmount = ?, tax = ?, vat = ?, pic = ?, stock = ?,
    ingredients = ?, preparationTime = ?, servingSize = ?, dailyCapacity = ?,
    discountType = ?, discountedAmount = ?, brandId = ?, businessId = ?,
    unitId = ?, modifiedBy = ?, modifiedOn = ?, updatedAt = ?
WHERE id = ?;

updateProductStock:
UPDATE ProductEntity 
SET stock = ?, updatedAt = ?
WHERE id = ?;

updateProductStockByProductId:
UPDATE ProductEntity 
SET stock = ?, updatedAt = ?
WHERE productId = ?;

updateProductPrice:
UPDATE ProductEntity 
SET price = ?, updatedAt = ?
WHERE id = ?;

markProductSynced:
UPDATE ProductEntity 
SET synced = 1, updatedAt = ?
WHERE id = ?;

markProductUnsynced:
UPDATE ProductEntity 
SET synced = 0, updatedAt = ?
WHERE id = ?;

markProductsSyncedByProductIds:
UPDATE ProductEntity 
SET synced = 1, updatedAt = ?
WHERE productId IN ?;

deleteProduct:
DELETE FROM ProductEntity WHERE id = ?;

deleteProductByProductId:
DELETE FROM ProductEntity WHERE productId = ?;

deleteProductsByStore:
DELETE FROM ProductEntity WHERE storeId = ?;

getProductCount:
SELECT COUNT(*) FROM ProductEntity;

getProductCountByStore:
SELECT COUNT(*) FROM ProductEntity WHERE storeId = ?;

getInStockProductCount:
SELECT COUNT(*) FROM ProductEntity WHERE stock = 1;

getInStockProductCountByStore:
SELECT COUNT(*) FROM ProductEntity WHERE stock = 1 AND storeId = ?;

getUnsyncedProductCount:
SELECT COUNT(*) FROM ProductEntity WHERE synced = 0;

-- Bulk operations
insertMultipleProducts:
INSERT INTO ProductEntity (
    productId, name, description, additionalInfo, category, categoryId,
    price, billAmount, tax, vat, pic, stock, ingredients, preparationTime,
    servingSize, dailyCapacity, discountType, discountedAmount, brandId,
    businessId, storeId, unitId, createdBy, createdOn, modifiedBy,
    modifiedOn, createdAt, updatedAt, synced
) VALUES ?;

-- Get products with pagination
getProductsPaginated:
SELECT * FROM ProductEntity 
WHERE storeId = ?
ORDER BY category ASC, name ASC 
LIMIT ? OFFSET ?; 