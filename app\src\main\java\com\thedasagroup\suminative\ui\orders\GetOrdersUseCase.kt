package com.thedasagroup.suminative.ui.orders

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.thedasagroup.suminative.data.model.request.login.BankDetail
import com.thedasagroup.suminative.data.model.request.login.Brand
import com.thedasagroup.suminative.data.model.request.login.Business
import com.thedasagroup.suminative.data.model.request.login.Category
import com.thedasagroup.suminative.data.model.request.login.ChangePassword
import com.thedasagroup.suminative.data.model.request.login.Cms
import com.thedasagroup.suminative.data.model.request.login.Conversation
import com.thedasagroup.suminative.data.model.request.login.Customer
import com.thedasagroup.suminative.data.model.request.login.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.login.DeliverySettingRange
import com.thedasagroup.suminative.data.model.request.login.DeliverySettings
import com.thedasagroup.suminative.data.model.request.login.Extra
import com.thedasagroup.suminative.data.model.request.login.ExtraItemRelation
import com.thedasagroup.suminative.data.model.request.login.FeedbackComplain
import com.thedasagroup.suminative.data.model.request.login.Option
import com.thedasagroup.suminative.data.model.request.login.OptionSet
import com.thedasagroup.suminative.data.model.request.login.Order
import com.thedasagroup.suminative.data.model.request.login.OrderRequest
import com.thedasagroup.suminative.data.model.request.login.OrderStatus
import com.thedasagroup.suminative.data.model.request.login.PaymentData
import com.thedasagroup.suminative.data.model.request.login.PromoCodes
import com.thedasagroup.suminative.data.model.request.login.Store
import com.thedasagroup.suminative.data.model.request.login.StoreItem
import com.thedasagroup.suminative.data.model.request.login.StoreSetting
import com.thedasagroup.suminative.data.model.request.login.SupportDetail
import com.thedasagroup.suminative.data.model.request.login.UiSettings
import com.thedasagroup.suminative.data.model.request.login.User
import com.thedasagroup.suminative.data.model.response.store_orders.OrdersResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.OrdersRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

open class GetOrdersUseCase(
    private val repo: OrdersRepository,
    private val prefs: Prefs
) {
    var orderCount = 0
    suspend operator fun invoke(isShowAllOrder: Boolean): StateFlow<Async<OrdersResponse>> {

        val orderResponse = repo.getOrders(
            request = OrderRequest(
                storeAdminId = -1,
                email = "",
                phoneNumber = "",
                storeId = prefs.store?.id ?: 105,
                storeSetting = StoreSetting(),
                cms = Cms(),
                order = Order(),
                bankDetail = BankDetail(),
                brand = Brand(),
                category = Category(),
                storeItem = StoreItem(),
                extra = Extra(),
                extraItemRelation = ExtraItemRelation(),
                appType = "cms",
                startDate = "",
                endDate = "",
                deliveryAddress = DeliveryAddress(),
                feedbackComplain = FeedbackComplain(),
                conversation = Conversation(),
                orderStatus = OrderStatus(),
                paymentData = PaymentData(),
                customer = Customer(),
                orderId = -1,
                pandaOrderId = "",
                optionSet = OptionSet(),
                option = Option(),
                optionSetId = -1,
                deliverySettingId = -1,
                deliverySettings = DeliverySettings(),
                deliverySettingRangeId = -1,
                deliverySettingRange = DeliverySettingRange(),
                uiSettings = UiSettings(),
                command = "getOrderForStore",
                user = User(),
                business = Business(),
                loggedUserId = -1,
                supportDetail = SupportDetail(),
                changePassword = ChangePassword(),
                businessId = -1,
                store = Store(),
                promoCodes = PromoCodes(),
            )
        )

        val orders : Async<OrdersResponse> = if (!isShowAllOrder) {
            when (orderResponse.value) {
                is Success -> {
                    orderCount = orderResponse.value()?.orders?.size ?: 0
                    val orders = orderResponse.value()?.orders?.filter { order ->
                        (order.status != 5) && (order.status != 6) && (order.status != 7)
                                && (order.status != 25) && (order.status != 26)
                    }
                    Success(OrdersResponse(orders = orders ?: emptyList(), success = orderResponse.value()?.success))
                }
                else -> {
                    orderResponse.value
                }
            }
        } else {
            when (orderResponse.value) {
                is Success -> {
                    orderCount = orderResponse.value()?.orders?.size ?: 0
                    val orders = orderResponse.value()?.orders
                    Success(OrdersResponse(orders = orders ?: emptyList(), success = orderResponse.value()?.success))
                }
                else -> {
                    orderResponse.value
                }
            }
        }
        return MutableStateFlow(orders)
    }
}