package com.thedasagroup.suminative.ui.reservations

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest
import com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import kotlinx.coroutines.flow.StateFlow
import java.util.TimeZone
import javax.inject.Inject

class CreateReservationUseCase @Inject constructor(
    private val reservationsRepository: ReservationsRepository
) {
    /**
     * Creates or updates a reservation with automatic timezone offset calculation
     * @param request The reservation request data
     * @param timezoneOffset Optional timezone offset in minutes east of UTC. If null, uses device timezone
     */
    suspend operator fun invoke(
        request: CreateReservationRequest,
        timezoneOffset: Int? = null
    ): StateFlow<Async<CreateReservationResponse>> {
        val finalTimezoneOffset = timezoneOffset ?: getDeviceTimezoneOffset()

        val requestWithTimezone = request.copy(
            timezoneOffset = finalTimezoneOffset
        )

        return reservationsRepository.createReservation(requestWithTimezone)
    }

    /**
     * Alternative method that accepts individual parameters and builds the request
     */
    suspend operator fun invoke(
        id: Int? = null,
        storeId: Int,
        tableId: Int,
        customerId: Int,
        guestName: String,
        guestPhone: String,
        numPeople: Int,
        reservationStatus: Int,
        reservationTime: String,
        timezoneOffset: Int? = null
    ): StateFlow<Async<CreateReservationResponse>> {
        val finalTimezoneOffset = timezoneOffset ?: getDeviceTimezoneOffset()

        val request = CreateReservationRequest(
            id = id,
            storeId = storeId,
            tableId = tableId,
            customerId = customerId,
            guestName = guestName,
            guestPhone = guestPhone,
            numPeople = numPeople,
            reservationStatus = reservationStatus,
            reservationTime = reservationTime,
            timezoneOffset = finalTimezoneOffset
        )

        return reservationsRepository.createReservation(request)
    }

    /**
     * Gets the device's current timezone offset in minutes east of UTC
     * @return Timezone offset in minutes (e.g., +5h = 300, -3h = -180)
     */
    private fun getDeviceTimezoneOffset(): Int {
        val timeZone = TimeZone.getDefault()
        return timeZone.rawOffset / (1000 * 60) // Convert milliseconds to minutes
    }
}
