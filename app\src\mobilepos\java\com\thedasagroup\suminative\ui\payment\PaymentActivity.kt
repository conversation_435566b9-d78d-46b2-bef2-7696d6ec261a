package com.thedasagroup.suminative.ui.payment

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.my_guava.failure.GuavaFailResponse
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import com.thedasagroup.suminative.ui.common.CancelledDialog
import com.thedasagroup.suminative.ui.common.FailureDialog
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlin.apply
import kotlin.jvm.java

/**
 * Activity to handle payment processing.
 * This is now a lightweight activity that shows the payment dialog.
 * This approach allows payment UI to be shown either as a dialog from other screens
 * or as a standalone activity for deeper integration.
 */
@AndroidEntryPoint
class PaymentActivity : AppCompatActivity(), MavericksView {
    
    val viewModel: PaymentViewModel by viewModel()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val order = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra(ARG_ORDER, Order::class.java)
        } else {
            @Suppress("DEPRECATION")
            intent.getParcelableExtra(ARG_ORDER)
        } ?: Order()
        
        setContent {
            SumiNativeTheme {
                val showFailureDialog = remember { mutableStateOf(false) }
                val showCancelledDialog = remember { mutableStateOf(false) }
                val failureMessage = remember { mutableStateOf("") }
                val cancelledMessage = remember { mutableStateOf("") }
                
                PaymentActivityScreen(
                    order = order,
                    onDismiss = { 
                        setResult(RESULT_CANCELED)
                        finish() 
                    },
                    onPaymentSuccess = { completedOrder ->
                        val resultIntent = Intent().apply {
                            putExtra(RESULT_ORDER, completedOrder)
                        }
                        setResult(RESULT_OK, resultIntent)
                        finish()
                    },
                    onMakePaymentClickFail = { guavaFailResponse ->
                        failureMessage.value = guavaFailResponse.error ?: "Payment failed"
                        showFailureDialog.value = true
                    },
                    onPaymentCancelled = {
                        cancelledMessage.value = "The payment has been cancelled successfully."
                        showCancelledDialog.value = true
                    },
                    viewModel = viewModel
                )
                
                // Failure Dialog
                FailureDialog(
                    message = failureMessage.value,
                    isVisible = showFailureDialog.value,
                    onDismiss = { 
                        showFailureDialog.value = false
                    }
                )
                
                // Cancelled Dialog
                CancelledDialog(
                    message = cancelledMessage.value,
                    isVisible = showCancelledDialog.value,
                    onDismiss = { 
                        showCancelledDialog.value = false
                        finish()
                    }
                )
            }
        }
    }

    override fun invalidate() {
        // Called when the state is updated
    }
    
    companion object {
        private const val ARG_ORDER = "extra_order"
        const val RESULT_ORDER = "result_order"
        
        fun createIntent(context: Context, order: Order): Intent {
            return Intent(context, PaymentActivity::class.java).apply {
                putExtra(ARG_ORDER, order)
            }
        }
    }
} 