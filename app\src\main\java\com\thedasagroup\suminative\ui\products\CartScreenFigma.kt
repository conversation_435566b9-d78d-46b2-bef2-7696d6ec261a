package com.thedasagroup.suminative.ui.products

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.TextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddCard
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Money
import androidx.compose.material.icons.filled.RemoveCircle
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.filled.Splitscreen
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.ui.theme.fontNunito
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.transformDecimal
import androidx.compose.ui.window.Dialog
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.ui.products.MealCourse
import com.thedasagroup.suminative.ui.products.CartItemWithCourse
import com.thedasagroup.suminative.ui.products.CourseFilter
import kotlin.collections.forEach
import kotlin.collections.isNullOrEmpty
import kotlin.let
import kotlin.text.isNotEmpty
import kotlin.text.isNullOrEmpty
import kotlin.text.uppercase

@Composable
fun CartScreenFigma(
    order: Order,
    onRemoveItem: (Cart) -> Unit,
    closeCart: () -> Unit,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
    onUpdateStock: (Int, StoreItem, Cart, OptionDetails?) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onSplitBillClick: (Int) -> Unit,
    onCloudPrintClick: (Order) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val optionDetails by productsScreenViewModel.collectAsState(ProductsScreenState::optionDetailsResponse)
    val state by productsScreenViewModel.collectAsState()

    MobileCartScreen(
        order = order,
        onRemoveItem = onRemoveItem,
        closeCart = closeCart,
        placeOrderCash = placeOrderCash,
        placeOrderCard = placeOrderCard,
        onUpdateStock = { stock, storeItem, cart ->
            // Create OptionDetails from the cart item's store item option sets
            val cartOptionDetails = OptionDetails(optionSets = storeItem.optionSets ?: mutableListOf())
            onUpdateStock(stock, storeItem, cart, cartOptionDetails)
        },
        onUpdateNotes = onUpdateNotes,
        onSplitBillClick = onSplitBillClick,
        onVoidItem = onVoidItem,
        onCloudPrintClick = onCloudPrintClick,
        productsScreenViewModel = productsScreenViewModel,
        state = state
    )
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun MobileCartScreen(
    order: Order,
    state: ProductsScreenState,
    onRemoveItem: (Cart) -> Unit,
    closeCart: () -> Unit,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onSplitBillClick: (Int) -> Unit,
    onCloudPrintClick: (Order) -> Unit,
    onVoidItem: (Cart) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {

    // Get state from ViewModel using Mavericks
    val availableCourses = state.availableCourses
    val cartItemsWithCourses = state.getCurrentTableCartItemsWithCourses()
    val selectedFilter = state.getCurrentTableCourseFilter()
    val courseCounts = state.getCourseCounts()
    val filteredCartItems = state.getFilteredCartItems()

    // Sync course assignments when order changes
    LaunchedEffect(order.carts) {
        productsScreenViewModel.syncCartItemsWithCourses()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
    ) {
        // Header with close button
        MobileCartHeader(closeCart = closeCart)

        // Course filter tabs
        CourseFilterTabs(
            selectedFilter = selectedFilter,
            onFilterSelected = { filter ->
                productsScreenViewModel.updateCourseFilter(filter)
            },
            courseCounts = courseCounts
        )

        // Cart Content
        if (filteredCartItems.isEmpty()) {
            MobileEmptyCart()
        } else {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                LazyColumn(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 16.dp, end = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(vertical = 16.dp)
                ) {
                    items(
                        items = filteredCartItems,
                        key = { cartItemWithCourse ->
                            "${cartItemWithCourse.cart.storeItem?.id}_${cartItemWithCourse.cart.quantity}_${cartItemWithCourse.courseId}_${cartItemWithCourse.cart.notes}"
                        }
                    ) { cartItemWithCourse ->
                        MobileCartItemCardWithCourse(
                            cartItemWithCourse = cartItemWithCourse,
                            availableCourses = availableCourses,
                            onRemoveItem = onRemoveItem,
                            onUpdateStock = onUpdateStock,
                            onUpdateNotes = onUpdateNotes,
                            onVoidItem = onVoidItem,
                            onCourseChanged = { newCourseId ->
                                productsScreenViewModel.updateCartItemCourse(
                                    cartItemId = cartItemWithCourse.cart.storeItem?.id,
                                    newCourseId = newCourseId
                                )
                            },
                            productsScreenViewModel = productsScreenViewModel
                        )
                    }

                    item {
                        // Create order summary for filtered items
                        val filteredOrder = order.copy(carts = filteredCartItems.map { it.cart })
                        MobileCartSummary(order = filteredOrder)
                    }
                }

                // Bottom checkout section
                MobileOrderTotalSticky(
                    order = order,
                    placeOrderCard = placeOrderCard,
                    placeOrderCash = placeOrderCash,
                    onSplitBillClick = onSplitBillClick,
                    onCloudPrintClick = onCloudPrintClick,
                    productsScreenViewModel = productsScreenViewModel
                )
            }
        }
    }
}

@Composable
private fun MobileCartHeader(closeCart: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        androidx.compose.material3.IconButton(
            onClick = closeCart,
            modifier = Modifier.size(40.dp)
        ) {
            androidx.compose.material3.Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Close cart",
                tint = Color(0xFF2E7D32)
            )
        }
    }
}

@Composable
fun CourseFilterTabs(
    selectedFilter: CourseFilter,
    onFilterSelected: (CourseFilter) -> Unit,
    courseCounts: Map<CourseFilter, Int>
) {
    ScrollableTabRow(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        selectedTabIndex = CourseFilter.values().indexOf(selectedFilter),
        containerColor = Color.Transparent,
        edgePadding = 0.dp,
        indicator = {
            // No indicator needed since we're using custom tab backgrounds
        }
    ) {
        CourseFilter.values().forEach { filter ->
            val isSelected = filter == selectedFilter
            val backgroundColor = if (isSelected) Color(0xFF2E7D32) else Color.White
            val textColor = if (isSelected) Color.White else Color(0xFF2E7D32)
            val borderColor = Color(0xFF2E7D32)
            val count = courseCounts[filter] ?: 0

            androidx.compose.material3.Card(
                modifier = Modifier
                    .clickable { onFilterSelected(filter) }
                    .padding(horizontal = 4.dp)
                    .border(
                        width = 2.dp,
                        color = borderColor,
                        shape = RoundedCornerShape(8.dp)
                    ),
                shape = RoundedCornerShape(8.dp),
                colors = CardDefaults.cardColors(containerColor = backgroundColor),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text(
                        text = filter.displayName,
                        color = textColor,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        fontFamily = fontPoppins
                    )

                    // Show item count for specific courses
                    if (filter != CourseFilter.ALL && count > 0) {
                        Text(
                            text = "($count)",
                            color = textColor.copy(alpha = 0.7f),
                            fontSize = 12.sp,
                            fontFamily = fontPoppins
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun MobileEmptyCart() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        androidx.compose.material3.Icon(
            painter = painterResource(R.drawable.cart_plus),
            contentDescription = "Empty cart",
            modifier = Modifier.size(80.dp),
            tint = Color(0xFF2E7D32).copy(alpha = 0.6f)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Your cart is empty",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            fontFamily = fontPoppins
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "Add items to get started",
            fontSize = 16.sp,
            color = Color.Black.copy(alpha = 0.7f),
            fontFamily = fontPoppins,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun MobileCartItemCardWithCourse(
    cartItemWithCourse: CartItemWithCourse,
    availableCourses: List<MealCourse>,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onCourseChanged: (String) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val cartItem = cartItemWithCourse.cart
    var showNotesDialog by remember { mutableStateOf(false) }
    var showCourseDialog by remember { mutableStateOf(false) }
    var itemNotes by remember { mutableStateOf(cartItem.notes ?: "") }

    if (showNotesDialog) {
        MobileOrderNotesDialog(
            initialNotes = itemNotes,
            onDismiss = { showNotesDialog = false },
            onConfirm = { notes ->
                itemNotes = notes
                val updateCartItem = cartItem.copy(notes = notes)
                onUpdateNotes(updateCartItem, notes)
                showNotesDialog = false
            }
        )
    }

    if (showCourseDialog) {
        MealCourseSelectionDialog(
            currentCourseId = cartItemWithCourse.courseId,
            availableCourses = availableCourses,
            onDismiss = { showCourseDialog = false },
            onCourseSelected = { courseId ->
                onCourseChanged(courseId)
                showCourseDialog = false
            }
        )
    }

    androidx.compose.material3.Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Course indicator badge
            val currentCourse = availableCourses.find { it.id == cartItemWithCourse.courseId }
            androidx.compose.material3.Card(
                modifier = Modifier
                    .padding(bottom = 8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF2E7D32).copy(alpha = 0.1f)
                ),
                shape = RoundedCornerShape(6.dp)
            ) {
                Text(
                    text = currentCourse?.displayName ?: "Course 1",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }

            // Header with item name and remove button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = cartItem.storeItem?.name?.uppercase() ?: "UNKNOWN",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black,
                        fontFamily = fontPoppins
                    )

                    // Action buttons row
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        androidx.compose.material3.TextButton(
                            onClick = { showNotesDialog = true },
                            contentPadding = PaddingValues(0.dp)
                        ) {
                            Text(
                                text = if (itemNotes.isNotEmpty()) "Edit Note" else "Add Note",
                                fontSize = 14.sp,
                                color = Color(0xFF2E7D32),
                                fontFamily = fontPoppins
                            )
                        }

                        androidx.compose.material3.TextButton(
                            onClick = { showCourseDialog = true },
                            contentPadding = PaddingValues(0.dp)
                        ) {
                            val currentCourse = availableCourses.find { it.id == cartItemWithCourse.courseId }
                            Text(
                                text = "Meal: ${currentCourse?.displayName ?: "Course 1"}",
                                fontSize = 14.sp,
                                color = Color(0xFF2E7D32),
                                fontFamily = fontPoppins
                            )
                        }
                    }
                }

                androidx.compose.material3.IconButton(
                    onClick = { onRemoveItem(cartItem) },
                    modifier = Modifier.size(40.dp)
                ) {
                    androidx.compose.material3.Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Remove item",
                        tint = Color.Red
                    )
                }
                if(productsScreenViewModel.prefs.storeConfigurations?.data?.quickService != true) {
                    Spacer(modifier = Modifier.width(8.dp))
                    androidx.compose.material3.TextButton(
                        onClick = { onVoidItem(cartItem) },
                    ) {
                        Text(
                            text = "Void Item",
                            fontSize = 14.sp,
                            color = Color.Red,
                            fontFamily = fontPoppins
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Item details
            cartItem.storeItem?.let { storeItem ->
                MobileCartItemDetails(storeItem = storeItem, cartQuantity = cartItem.quantity ?: 1)
            }

            // Notes section
            if (itemNotes.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                androidx.compose.material3.Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFF5F5F5)
                    )
                ) {
                    Column(modifier = Modifier.padding(8.dp)) {
                        Text(
                            text = "Notes:",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.Black,
                            fontFamily = fontPoppins
                        )
                        Text(
                            text = itemNotes,
                            fontSize = 12.sp,
                            color = Color.Black.copy(alpha = 0.8f),
                            fontStyle = FontStyle.Italic,
                            fontFamily = fontPoppins,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Quantity and price section
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                MobileStockUpdateCounterCart(
                    initialStock = cartItem.quantity ?: 1
                ) { stock ->
                    onUpdateStock(stock, cartItem.storeItem ?: StoreItem(), cartItem)
                }

                Text(
                    text = "£${cartItem.netPayable?.transformDecimal() ?: "0.00"}",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            }
        }
    }
}

@Composable
private fun MobileCartItemDetails(storeItem: StoreItem, cartQuantity: Int) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Main item - use cart quantity instead of store item quantity
        MobileCartItemRow(
            quantity = cartQuantity,
            name = storeItem.name ?: "",
            price = (storeItem.price ?: 0.0) * cartQuantity
        )

        // Options
        storeItem.optionSets?.forEach { optionSet ->
            optionSet.options?.forEach { option ->
                if ((option?.quantity ?: 0) > 0) {
                    MobileCartItemRow(
                        quantity = option?.quantity ?: 0,
                        name = option?.name ?: "",
                        price = (option?.price ?: 0.0) * (option?.quantity ?: 0) * cartQuantity,
                        isExtra = true
                    )
                }
            }
        }
    }
}

@Composable
private fun MobileCartItemRow(
    quantity: Int,
    name: String,
    price: Double,
    isExtra: Boolean = false
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "${if (isExtra) "  + " else ""}$quantity x $name",
            fontSize = 14.sp,
            color = if (isExtra) Color.Black.copy(alpha = 0.7f) else Color.Black,
            fontFamily = fontPoppins,
            modifier = Modifier.weight(1f)
        )

        Text(
            text = "£${price.transformDecimal()}",
            fontSize = 14.sp,
            color = Color.Black,
            fontWeight = if (isExtra) FontWeight.Normal else FontWeight.Medium,
            fontFamily = fontPoppins
        )
    }
}

@Composable
private fun MobileCartSummary(order: Order) {
    androidx.compose.material3.Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "Order Summary",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2E7D32),
                fontFamily = fontPoppins
            )

            MobileSummaryRow(
                title = "Items (${order.carts?.size ?: 0})",
                value = "£${order.netPayable?.transformDecimal() ?: "0.00"}"
            )

            MobileSummaryRow(
                title = "Taxes",
                value = "£${order.tax?.transformDecimal() ?: "0.00"}"
            )

            HorizontalDivider(
                modifier = Modifier.padding(vertical = 4.dp),
                color = Color.Black.copy(alpha = 0.2f)
            )

            MobileSummaryRow(
                title = "Total Payable",
                value = "£${order.totalPrice?.transformDecimal() ?: "0.00"}",
                isBold = true,
                valueColor = Color(0xFF2E7D32)
            )
        }
    }
}

@Composable
private fun MobileSummaryRow(
    title: String,
    value: String,
    isBold: Boolean = false,
    valueColor: Color = Color.Black
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            fontSize = 14.sp,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal,
            color = Color.Black,
            fontFamily = fontPoppins
        )

        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal,
            color = valueColor,
            fontFamily = fontPoppins
        )
    }
}

@Composable
private fun MobileOrderTotalSticky(
    order: Order,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
    onSplitBillClick: (Int) -> Unit,
    onCloudPrintClick : (Order) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val ordersResponse by productsScreenViewModel.collectAsState(ProductsScreenState::orderResponse)
    var showSplitBillDialog by remember { mutableStateOf(false) }

    if (showSplitBillDialog) {
        SplitBillDialog(
            onDismiss = { showSplitBillDialog = false },
            onConfirm = { numberOfPersons ->
                showSplitBillDialog = false
                onSplitBillClick(numberOfPersons)
            }
        )
    }

    androidx.compose.material3.Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 48.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Total amount
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Total",
                        fontSize = 16.sp,
                        color = Color.Black.copy(alpha = 0.7f),
                        fontFamily = fontPoppins
                    )
                    Text(
                        text = "£${order.totalPrice?.transformDecimal() ?: "0.00"}",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2E7D32),
                        fontFamily = fontPoppins
                    )
                }
            }

            // Payment buttons
            if (ordersResponse is Loading) {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFF2E7D32)
                    )
                }
            } else {
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Split Bill Button
                    if (productsScreenViewModel.prefs.storeConfigurations?.data?.quickService != true) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            androidx.compose.material3.OutlinedButton(
                                onClick = {
                                    onCloudPrintClick(order)
                                },
                                modifier = Modifier
                                    .weight(1f)
                                    .height(56.dp),
                                shape = RoundedCornerShape(12.dp),
                                border = BorderStroke(2.dp, Color(0xFF2E7D32)),
                                colors = ButtonDefaults.outlinedButtonColors(
                                    contentColor = Color(0xFF2E7D32)
                                )
                            ) {
                                androidx.compose.material3.Icon(
                                    imageVector = Icons.Default.AddCard,
                                    contentDescription = null,
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Cloud Print",
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp,
                                    fontFamily = fontPoppins
                                )
                            }

                            Button(
                                onClick = {
                                    showSplitBillDialog = true
                                },
                                modifier = Modifier
                                    .weight(1f)
                                    .height(56.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF2E7D32),
                                    contentColor = Color.White
                                ),
                                shape = RoundedCornerShape(12.dp)
                            ) {
                                androidx.compose.material3.Icon(
                                    imageVector = Icons.Default.Money,
                                    contentDescription = null,
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Split Bill Payment",
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp,
                                    fontFamily = fontPoppins,
                                    color = Color.White
                                )
                            }
//                            androidx.compose.material3.OutlinedButton(
//                                onClick = {
//                                    onCloudPrintClick(order)
//                                },
//                                modifier = Modifier
//                                    .weight(1f)
//                                    .height(56.dp),
//                                shape = RoundedCornerShape(12.dp),
//                                border = BorderStroke(2.dp, Color(0xFF2E7D32)),
//                                colors = ButtonDefaults.outlinedButtonColors(
//                                    contentColor = Color(0xFF2E7D32)
//                                )
//                            ) {
//                                androidx.compose.material3.Icon(
//                                    imageVector = Icons.Default.AddCard,
//                                    contentDescription = null,
//                                    modifier = Modifier.size(20.dp)
//                                )
//                                Spacer(modifier = Modifier.width(8.dp))
//                                Text(
//                                    text = "Cloud Print",
//                                    fontWeight = FontWeight.Bold,
//                                    fontSize = 16.sp,
//                                    fontFamily = fontPoppins
//                                )
//                            }
//                            Button(
//                                onClick = { showSplitBillDialog = true },
//                                modifier = Modifier
//                                    .fillMaxWidth()
//                                    .height(56.dp),
//                                colors = ButtonDefaults.buttonColors(
//                                    containerColor = Color(0xFF2E7D32),
//                                    contentColor = Color.White
//                                ),
//                                shape = RoundedCornerShape(12.dp)
//                            ) {
//                                androidx.compose.material3.Icon(
//                                    imageVector = Icons.Default.Splitscreen,
//                                    contentDescription = null,
//                                    modifier = Modifier.size(20.dp)
//                                )
//                                Spacer(modifier = Modifier.width(8.dp))
//                                Text(
//                                    text = "Split Bill Payment",
//                                    fontWeight = FontWeight.Bold,
//                                    fontSize = 16.sp,
//                                    fontFamily = fontPoppins,
//                                    color = Color.White
//                                )
//                            }
                        }
                    }

                        // Regular Payment Buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        androidx.compose.material3.OutlinedButton(
                            onClick = placeOrderCard,
                            modifier = Modifier
                                .weight(1f)
                                .height(56.dp),
                            shape = RoundedCornerShape(12.dp),
                            border = BorderStroke(2.dp, Color(0xFF2E7D32)),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = Color(0xFF2E7D32)
                            )
                        ) {
                            androidx.compose.material3.Icon(
                                imageVector = Icons.Default.AddCard,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Card",
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp,
                                fontFamily = fontPoppins
                            )
                        }

                        Button(
                            onClick = placeOrderCash,
                            modifier = Modifier
                                .weight(1f)
                                .height(56.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            androidx.compose.material3.Icon(
                                imageVector = Icons.Default.Money,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Cash",
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun MobileStockUpdateCounterCart(
    initialStock: Int = 1,
    onStockChange: (Int) -> Unit,
) {
    var stock = initialStock
//    var stock by remember(initialStock) { mutableStateOf(initialStock) }
//
//    // Update stock when initialStock changes (when cart item quantity is updated externally)
//    LaunchedEffect(initialStock) {
//        stock = initialStock
//    }

    Row(
        modifier = Modifier
            .background(
                Color(0xFF2E7D32),
                RoundedCornerShape(8.dp)
            )
            .padding(4.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        androidx.compose.material3.IconButton(
            onClick = {
                if (stock > 1) {
                    stock--
                    onStockChange(stock)
                }
            },
            modifier = Modifier.size(32.dp)
        ) {
            androidx.compose.material3.Icon(
                imageVector = Icons.Default.RemoveCircle,
                contentDescription = "Decrease",
                modifier = Modifier.size(20.dp),
                tint = Color.White
            )
        }

        Text(
            text = stock.toString(),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(horizontal = 8.dp),
            color = Color.White,
            fontFamily = fontPoppins
        )

        androidx.compose.material3.IconButton(
            onClick = {
                stock++
                onStockChange(stock)
            },
            modifier = Modifier.size(32.dp)
        ) {
            androidx.compose.material3.Icon(
                imageVector = Icons.Default.AddCircle,
                contentDescription = "Increase",
                modifier = Modifier.size(20.dp),
                tint = Color.White
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MobileOrderNotesDialog(
    initialNotes: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var notes by remember { mutableStateOf(initialNotes) }

    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Add Order Notes",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                OutlinedTextField(
                    value = notes,
                    onValueChange = { notes = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    placeholder = {
                        Text(
                            "Enter notes for this item...",
                            color = Color.Black.copy(alpha = 0.6f),
                            fontFamily = fontPoppins
                        )
                    },
                    maxLines = 5,
                    shape = RoundedCornerShape(8.dp),
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = Color(0xFF2E7D32),
                        unfocusedBorderColor = Color.Black.copy(alpha = 0.3f),
                        focusedTextColor = Color.Black,
                        unfocusedTextColor = Color.Black
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    androidx.compose.material3.TextButton(
                        onClick = onDismiss,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color.Black.copy(alpha = 0.7f)
                        )
                    ) {
                        Text(
                            "Cancel",
                            fontFamily = fontPoppins
                        )
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    androidx.compose.material3.TextButton(
                        onClick = { onConfirm(notes) },
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF2E7D32)
                        )
                    ) {
                        Text(
                            "Save",
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MealCourseSelectionDialog(
    currentCourseId: String,
    availableCourses: List<MealCourse>,
    onDismiss: () -> Unit,
    onCourseSelected: (String) -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Select Meal Course",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Course selection options
                availableCourses.forEach { course ->
                    val isSelected = course.id == currentCourseId
                    val backgroundColor = if (isSelected) Color(0xFF2E7D32) else Color.White
                    val textColor = if (isSelected) Color.White else Color(0xFF2E7D32)
                    val borderColor = Color(0xFF2E7D32)

                    androidx.compose.material3.Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp)
                            .clickable { onCourseSelected(course.id) }
                            .border(
                                width = 2.dp,
                                color = borderColor,
                                shape = RoundedCornerShape(8.dp)
                            ),
                        shape = RoundedCornerShape(8.dp),
                        colors = CardDefaults.cardColors(containerColor = backgroundColor),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        Text(
                            text = course.displayName,
                            color = textColor,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Cancel button
                androidx.compose.material3.TextButton(
                    onClick = onDismiss,
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = Color.Black.copy(alpha = 0.7f)
                    )
                ) {
                    Text(
                        "Cancel",
                        fontFamily = fontPoppins
                    )
                }
            }
        }
    }
}

@Composable
private fun MobileCartItemCard(
    cartItem: Cart,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val state by productsScreenViewModel.collectAsState()

    // Get state from ViewModel
    val availableCourses = state.availableCourses
    val cartItemsWithCourses = state.getCurrentTableCartItemsWithCourses()

    // Find the course assignment for this cart item, default to Course 1
    val cartItemWithCourse = cartItemsWithCourses.find { it.cart.storeItem?.id == cartItem.storeItem?.id }
        ?: CartItemWithCourse(cart = cartItem, courseId = "course_1")

    MobileCartItemCardWithCourse(
        cartItemWithCourse = cartItemWithCourse,
        availableCourses = availableCourses,
        onRemoveItem = onRemoveItem,
        onUpdateStock = onUpdateStock,
        onUpdateNotes = onUpdateNotes,
        onVoidItem = onVoidItem,
        onCourseChanged = { newCourseId ->
            productsScreenViewModel.updateCartItemCourse(
                cartItemId = cartItem.storeItem?.id,
                newCourseId = newCourseId
            )
        },
        productsScreenViewModel = productsScreenViewModel
    )
}

@Composable
fun StockUpdateCounterCart(
    initialStock: Int = 1, onStockChange: (Int) -> Unit
) {
    var stock by remember { mutableStateOf(initialStock) }

    Row(
        modifier = Modifier
            .padding(8.dp)
            .wrapContentWidth()
            .border(
                width = 1.dp,
                color = Color(0xeFFbf0ff),
                shape = RoundedCornerShape(1.dp)
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        androidx.compose.material3.IconButton(onClick = {
            if (stock > 1) {
                stock--
                onStockChange(stock)
            }
        }) {
            androidx.compose.material3.Icon(
                modifier = Modifier
                    .background(Color.White)
                    .padding(8.dp),
                painter = painterResource(id = R.drawable.cart_minus),
                contentDescription = "Decrease"
            )
        }

        Text(
            text = stock.toString(), style = TextStyle(
                fontFamily = fontPoppins,
                fontWeight = FontWeight.Normal,
                fontSize = 12.sp
            ), modifier = Modifier
                .background(Color(0xFFebf0ff))
                .padding(8.dp)
                .width(48.dp),
            textAlign = TextAlign.Center
        )

        androidx.compose.material3.IconButton(onClick = {
            stock++
            onStockChange(stock)
        }) {
            androidx.compose.material3.Icon(
                modifier = Modifier
                    .background(Color.White)
                    .padding(8.dp),
                painter = painterResource(id = R.drawable.cart_plus),
                contentDescription = "Decrease"
            )
        }
    }
}

@Composable
fun CartSummery(
    order: Order
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 15.dp, end = 10.dp, top = 20.dp, bottom = 20.dp),
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            TotalCartFigma(
                title = "Items (${order.carts?.size ?: 0})",
                value = "£ ${order.netPayable?.transformDecimal()}",
                isBold = false,
                style = TextStyle(
                    fontFamily = fontPoppins,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                )
            )
            Spacer(modifier = Modifier.height(12.dp))
            TotalCartFigma(
                title = "Taxes",
                value = "£ ${order.tax?.transformDecimal()}",
                isBold = false,
                style = TextStyle(
                    fontFamily = fontPoppins,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                )
            )
            Spacer(modifier = Modifier.height(12.dp))
            TotalCartFigma(
                title = "Total Payable",
                value = "£ ${order.totalPrice?.transformDecimal()}",
                isBold = true,
                style = TextStyle(
                    fontFamily = fontPoppins,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF40bfff)
                )
            )
        }
    }
}

@Composable
fun OrderTotalSticky(
    productsScreenViewModel: ProductsScreenViewModel,
    order: Order,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
) {
    val ordersResponse by productsScreenViewModel.collectAsState(ProductsScreenState::orderResponse)
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 15.dp, end = 10.dp, top = 20.dp, bottom = 20.dp),
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            TotalCartFigma(
                title = "Total Payable",
                value = "£ ${order.totalPrice?.transformDecimal()}",
                isBold = true,
                style = TextStyle(
                    fontFamily = fontPoppins,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF40bfff)
                )
            )
            Spacer(modifier = Modifier.height(5.dp))
            Text(
                modifier = Modifier
                    .padding(start = 2.dp)
                    .width(170.dp),
                text = "Summary",
                style = TextStyle(
                    fontFamily = fontPoppins,
                    fontWeight = FontWeight.Normal,
                    fontSize = 10.sp,
                    fontStyle = FontStyle.Normal,
                    textDecoration = TextDecoration.Underline
                )
            )
            Spacer(modifier = Modifier.height(5.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (ordersResponse is Loading) {
                    CircularProgressIndicator(color = Color.Blue)
                } else {
                    TextButton(
                        onClick = {
                            placeOrderCard()
                        },
                        modifier = Modifier
                            .background(color = Color.Green)
                            .padding(5.dp),
                        shape = CircleShape
                    ) {
                        Text(
                            text = "Card", color = Color.Black,
                            style = TextStyle(
                                fontFamily = fontPoppins,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold
                            )
                        )
                    }
                    Spacer(modifier = Modifier.width(20.dp))
                    TextButton(
                        onClick = {
                            placeOrderCash()
                        },
                        modifier = Modifier
                            .background(color = Color.Red)
                            .padding(5.dp),
                        shape = CircleShape
                    ) {
                        Text(
                            text = "Cash", color = Color.White,
                            style = TextStyle(
                                fontFamily = fontPoppins,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold
                            )
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun CartItemCard(
    cartItem: Cart,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit
) {
    var showNotesDialog by remember { mutableStateOf(false) }
    var itemNotes by remember { mutableStateOf(cartItem.notes ?: "") }

    if (showNotesDialog) {
        OrderNotesDialog(
            initialNotes = itemNotes,
            onDismiss = { showNotesDialog = false },
            onConfirm = { notes ->
                itemNotes = notes
                val updateCartItem = cartItem.copy(notes = notes)
                onUpdateNotes(updateCartItem, notes)
                showNotesDialog = false
            }
        )
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 15.dp, end = 10.dp, top = 20.dp, bottom = 20.dp),
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(end = 10.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier) {
                    Text(
                        text = cartItem.storeItem?.name ?: "",
                        fontFamily = fontNunito,
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    )
                    Spacer(modifier = Modifier.padding(5.dp))
                    TextButton(
                        onClick = { showNotesDialog = true },
                        colors = androidx.compose.material.ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF3F51B5)
                        )
                    ) {
                        Text(
                            "Add Note",
                        )
                    }
                }
                IconButton(
                    modifier = Modifier.width(24.dp), onClick = {
                        onRemoveItem(cartItem)
                    },) {
                    Icon(
                        painter = painterResource(R.drawable.trash),
                        contentDescription = "Remove",
                        modifier = Modifier.size(24.dp),
                    )
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
            cartItem.storeItem?.let { storeItem ->
                TotalCartFigma(
                    title = "☐ ${storeItem?.quantity} x ${storeItem?.name}",
                    value = "£ ${((storeItem?.price ?: 0.0) * (storeItem?.quantity ?: 0)).transformDecimal()}",
                    isBold = true
                )
                Spacer(modifier = Modifier.height(16.dp))
                storeItem?.extras?.forEach { element ->
                    TotalCartFigma(
                        title = "☐ ${element.quantity} x ${element.name}",
                        value = "£ ${((storeItem.price ?: 0.0) * (element.quantity ?: 0)).transformDecimal()}",
                    )
                    Spacer(modifier = Modifier.padding(4.dp))
                }
                storeItem?.optionSets?.forEach { element ->
                    element.options?.forEach { option ->
                        if ((option?.quantity ?: 0) > 0) {
                            TotalCartFigma(
                                title = "☐ ${option?.quantity} x ${option?.name}",
                                value = "£ ${((option?.price ?: 0.0) * (option?.quantity ?: 0) * (storeItem.quantity ?: 0)).transformDecimal()}"
                            )
                            Spacer(modifier = Modifier.padding(4.dp))
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
            // Display notes if they exist
            if (!itemNotes.isNullOrEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    backgroundColor = Color(0xFFF5F5F5)
                ) {
                    Column(modifier = Modifier.padding(8.dp)) {
                        Text(
                            text = "Notes:",
                            style = TextStyle(
                                fontFamily = fontPoppins,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold
                            )
                        )
                        Text(
                            text = itemNotes,
                            style = TextStyle(
                                fontFamily = fontPoppins,
                                fontSize = 12.sp,
                                fontStyle = FontStyle.Italic
                            ),
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(end = 10.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                StockUpdateCounterCart { st ->
                    onUpdateStock(st, cartItem.storeItem ?: StoreItem(), cartItem)
                }
                Text(
                    modifier = Modifier.width(70.dp),
                    text = "£ ${cartItem.netPayable?.transformDecimal() ?: ""}",
                    style = TextStyle(
                        fontFamily = fontPoppins,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF40bfff)
                    ),
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
fun OrderNotesDialog(
    initialNotes: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var notes by remember { mutableStateOf(initialNotes) }

    Dialog(onDismissRequest = {
        onDismiss()
    }) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Add Order Notes",
                    style = TextStyle(
                        fontFamily = fontPoppins,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    ),
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                TextField(
                    value = notes,
                    onValueChange = { notes = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    placeholder = { Text("Enter notes for this item...") },
                    maxLines = 5
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    TextButton(
                        onClick = { onConfirm(notes) },
                        colors = androidx.compose.material.ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF40bfff)
                        )
                    ) {
                        Text("Save")
                    }
                }
            }
        }
    }
}

@Composable
fun TotalCartFigma(
    title: String, value: String, style: TextStyle = TextStyle(
        fontFamily = fontNunito,
        fontSize = 14.sp,
        fontWeight = FontWeight.Normal,
    ), isBold: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        androidx.compose.material3.Text(
            modifier = Modifier.width(180.dp),
            text = title,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
        androidx.compose.material3.Text(
            modifier = Modifier.width(80.dp),
            text = value,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SplitBillDialog(
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit
) {
    var numberOfPersons by remember { mutableStateOf("") }
    var isError by remember { mutableStateOf(false) }

    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Split Bill Payment",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )

                Text(
                    text = "Enter the number of persons to split the bill",
                    fontSize = 16.sp,
                    color = Color.Black.copy(alpha = 0.7f),
                    fontFamily = fontPoppins,
                    textAlign = TextAlign.Center
                )

                OutlinedTextField(
                    value = numberOfPersons,
                    onValueChange = { newValue ->
                        // Only allow numeric input
                        if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.toIntOrNull() != null)) {
                            numberOfPersons = newValue
                            // Validate the number
                            val number = newValue.toIntOrNull()
                            isError = number != null && (number < 1 || number > 20)
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    label = {
                        Text(
                            "Number of Persons",
                            fontFamily = fontPoppins
                        )
                    },
                    placeholder = {
                        Text(
                            "Enter number (1-20)",
                            color = Color.Black.copy(alpha = 0.6f),
                            fontFamily = fontPoppins
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    isError = isError,
                    supportingText = if (isError) {
                        { Text(
                            "Number must be between 1 and 20",
                            color = Color.Red,
                            fontFamily = fontPoppins
                        ) }
                    } else null,
                    shape = RoundedCornerShape(8.dp),
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = Color(0xFF2E7D32),
                        unfocusedBorderColor = Color.Black.copy(alpha = 0.3f),
                        focusedTextColor = Color.Black,
                        unfocusedTextColor = Color.Black,
                        focusedLabelColor = Color(0xFF2E7D32),
                        unfocusedLabelColor = Color.Black.copy(alpha = 0.6f)
                    )
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    androidx.compose.material3.OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        shape = RoundedCornerShape(8.dp),
                        border = BorderStroke(1.dp, Color.Black.copy(alpha = 0.3f)),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Black.copy(alpha = 0.7f)
                        )
                    ) {
                        Text(
                            "Cancel",
                            fontFamily = fontPoppins,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    Button(
                        onClick = {
                            val number = numberOfPersons.toIntOrNull()
                            if (number != null && number in 1..20) {
                                onConfirm(number)
                            }
                        },
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        enabled = numberOfPersons.isNotEmpty() && !isError && numberOfPersons.toIntOrNull() != null,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32),
                            contentColor = Color.White,
                            disabledContainerColor = Color.Gray.copy(alpha = 0.3f),
                            disabledContentColor = Color.Gray
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            "Open Split Bill Screen",
                            fontWeight = FontWeight.Bold,
                            fontFamily = fontPoppins
                        )
                    }
                }
            }
        }
    }
}
