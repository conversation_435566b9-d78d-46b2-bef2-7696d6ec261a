package com.thedasagroup.suminative.ui.reservations

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

class CancelReservationUseCase @Inject constructor(
    private val reservationsRepository: ReservationsRepository
) {
    suspend operator fun invoke(reservationId: Int): StateFlow<Async<Boolean>> {
        return reservationsRepository.cancelReservation(reservationId)
    }
}
