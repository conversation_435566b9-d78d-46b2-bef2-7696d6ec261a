package com.thedasagroup.suminative.data.model.request.my_guava.orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CreateOrderRequest(
    @SerialName("additionalAmount")
    val additionalAmount: String? = null,
    @SerialName("cashbackAmount")
    val cashbackAmount: String? = null,
    @SerialName("ccy")
    val ccy: String,
    @SerialName("clientReference")
    val clientReference: String,
    @SerialName("description")
    val description: String? = null,
    @SerialName("device")
    val device: String? = null,
    @SerialName("expireAt")
    val expireAt: String,
    @SerialName("ipAddress")
    val ipAddress: String? = null,
    @SerialName("paymentMethod")
    val paymentMethod: String,
    @SerialName("serviceChargeAmount")
    val serviceChargeAmount: String? = null,
    @SerialName("taxAmount")
    val taxAmount: String,
    @SerialName("taxId")
    val taxId: String? = null,
    @SerialName("taxName")
    val taxName: String? = null,
    @SerialName("tipsAmount")
    val tipsAmount: String? = null,
    @SerialName("totalAmount")
    val totalAmount: String,
    @SerialName("transactionAmount")
    val transactionAmount: String,
    @SerialName("transactionType")
    val transactionType: String,
    @SerialName("userAgent")
    val userAgent: String? = null
)