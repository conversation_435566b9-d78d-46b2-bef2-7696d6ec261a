package com.thedasagroup.suminative.data.model.response.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class User(
    @SerialName("businessId") val businessId: Int? = null,
    @SerialName("createdBy") val createdBy: Int? = null,
    @SerialName("createdOn") val createdOn: String? = null,
    @SerialName("dob") val dob: String? = null,
    @SerialName("email") val email: String? = null,
    @SerialName("emailVerified") val emailVerified: Boolean? = false,
    @SerialName("gender") val gender: String? = null,
    @SerialName("id") val id: Int? = null,
    @SerialName("modifiedBy") val modifiedBy: Int? = null,
    @SerialName("modifiedOn") val modifiedOn: String? = null,
    @SerialName("name") val name: String? = null,
    @SerialName("password") val password: String? = null,
    @SerialName("phone") val phone: String? = null,
    @SerialName("phoneVerified") val phoneVerified: Boolean? = false,
    @SerialName("pic") val pic: String? = null,
    @SerialName("storeId") val storeId: Int? = null,
    @SerialName("userGroupId") val userGroupId: Int? = null,
)