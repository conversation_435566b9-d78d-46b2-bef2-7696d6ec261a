package com.thedasagroup.suminative.data.model.request.order

import android.os.Parcelable
import com.thedasagroup.suminative.data.model.response.store_orders.Option
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@Parcelize
data class OptionSet(
    @SerialName("condition")
    val condition: Int? = null,
    @SerialName("customNumber")
    val customNumber: Int? = null,
    @SerialName("displayOrder")
    val displayOrder: Int? = null,
    @SerialName("id")
    val id: Int? = 0,
    @SerialName("itemId")
    val itemId: Int? = 0,
    @SerialName("name")
    val name: String? = null,
    @SerialName("options")
    val options: List<Option?> = mutableListOf(),
    @SerialName("status")
    val status: Int? = 0,
    @SerialName("variantType")
    val variantType: Int? = 0
) : Parcelable