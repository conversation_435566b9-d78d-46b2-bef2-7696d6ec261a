package com.thedasagroup.suminative.ui.payment

import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import androidx.fragment.app.FragmentActivity
import com.thedasagroup.suminative.data.model.request.order.Order

/**
 * Helper object to show the Payment as Activity instead of Dialog
 */
object PaymentCompose {

    /**
     * Show payment as full screen activity
     */
    fun showPaymentActivity(
        activity: FragmentActivity,
        order: Order,
        launcher: ActivityResultLauncher<Intent>
    ) {
        val intent = PaymentActivity.createIntent(activity, order)
        launcher.launch(intent)
    }
}