package com.thedasagroup.suminative.data.model.request.order

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@Parcelize
data class Order(
    @SerialName("businessId")
    val businessId: Int?= 0,
    @SerialName("carts")
    val carts: List<Cart>?= emptyList(),
    @SerialName("createdBy")
    val createdBy: Int?= 0,
    @SerialName("createdOn")
    val createdOn: String?= null,
    @SerialName("customer")
    val customer: Customer?= null,
    @SerialName("customerId")
    val customerId: Int?= 0,
    @SerialName("deliveryAddress")
    val deliveryAddress: DeliveryAddress?= null,
    @SerialName("deliveryCharges")
    val deliveryCharges: Double?= 0.0,
    @SerialName("deliveryNote")
    val deliveryNote: String?= null,
    @SerialName("deliveryType")
    val deliveryType: Int?= 0,
    @SerialName("discountONPromo")
    val discountONPromo: Double?= 0.0,
    @SerialName("guest")
    val guest: Boolean?= false,
    @SerialName("id")
    val id: Int?= -1,
    @SerialName("isPromoCodeAvailed")
    val isPromoCodeAvailed: Boolean?= false,
    @SerialName("lastUpdatedFromPanda")
    val lastUpdatedFromPanda: String?= null,
    @SerialName("modifiedBy")
    val modifiedBy: Int?= 0,
    @SerialName("modifiedOn")
    val modifiedOn: String?= null,
    @SerialName("netPayable")
    val netPayable: Double?= 0.0,
    @SerialName("orderStatusHistory")
    val orderStatusHistory: List<Order>?= emptyList(),
    @SerialName("pandaOrderDetail")
    val pandaOrderDetail: PandaOrderDetail?= null,
    @SerialName("pandaOrderId")
    val pandaOrderId: String?= null,
    @SerialName("paymentId")
    val paymentId: Int?= 0,
    @SerialName("paymentType")
    val paymentType: Int?= 0,
    @SerialName("pickupTime")
    val pickupTime: String?= null,
    @SerialName("priceAfterPromo")
    val priceAfterPromo: Int?= 0,
    @SerialName("priceBeforePromo")
    val priceBeforePromo: Int?= 0,
    @SerialName("promoCode")
    val promoCode: String?= null,
    @SerialName("scheduled")
    val scheduled: Boolean?= false,
    @SerialName("scheduledDateTime")
    val scheduledDateTime: String?= null,
    @SerialName("status")
    val status: Int?= 0,
    @SerialName("storeId")
    val storeId: Int?= 0,
    @SerialName("tax")
    val tax: Double?= 0.0,
    @SerialName("totalDiscount")
    val totalDiscount: Double?= 0.0,
    @SerialName("totalExtraPrice")
    val totalExtraPrice: Double?= 0.0,
    @SerialName("totalOptionPrice")
    val totalOptionPrice: Double?= 0.0,
    @SerialName("totalPrice")
    val totalPrice: Double?= 0.0,
    @SerialName("trackingUrl")
    val trackingUrl: String?= null,
    @SerialName("transactionId")
    val transactionId: String?= null,
    @SerialName("orderNotes")
    val orderNotes: String?= null,
) : Parcelable