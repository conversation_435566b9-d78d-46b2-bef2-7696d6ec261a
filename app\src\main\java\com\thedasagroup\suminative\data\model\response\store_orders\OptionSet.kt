package com.thedasagroup.suminative.data.model.response.store_orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OptionSet(
    @SerialName("condition")
    val condition: Int? = null,
    @SerialName("customNumber")
    val customNumber: Int? = null,
    @SerialName("displayOrder")
    val displayOrder: Int? = null,
    @SerialName("id")
    val id: Int? = null,
    @SerialName("itemId")
    val itemId: Int? = null,
    @SerialName("name")
    val name: String? = null,
    @SerialName("options")
    val options: List<Option>? = mutableListOf(),
    @SerialName("status")
    val status: Int? = null,
    @SerialName("variantType")
    val variantType: Int? = null
)