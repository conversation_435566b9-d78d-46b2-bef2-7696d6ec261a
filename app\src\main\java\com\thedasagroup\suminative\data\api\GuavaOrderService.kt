package com.thedasagroup.suminative.data.api

import com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GetListOfOrdersResponse
import retrofit2.Call
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Query

interface GuavaOrderService {
    @GET("/v1/epos/orders")
    suspend fun listOrders(
        @Query("size") size: Int,
        @Query("page") page: Int,
        @Query("dateTo") dateTo: String,
        @Header("x-client-key") authKey: String,
    ): Response<GetListOfOrdersResponse?>
}