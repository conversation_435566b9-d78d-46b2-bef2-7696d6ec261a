package com.thedasagroup.suminative.data.model.response.order

import com.thedasagroup.suminative.data.model.response.store_orders.Order
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OrderResponse(
    @SerialName("command") val command: String? = null,
    @SerialName("customerId") val customerId: Int? = 0,
    @SerialName("message") val message: String? = null,
    @SerialName("order") val order: Order? = null,
    @SerialName("payment") val paymentId: Int? = 0,
    @SerialName("paymentResponseJson") val paymentResponseJson: String? = null,
    @SerialName("success") val success: Boolean? = false
)