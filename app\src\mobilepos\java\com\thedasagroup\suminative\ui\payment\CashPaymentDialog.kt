package com.thedasagroup.suminative.ui.payment

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Backspace
import androidx.compose.material.icons.filled.Cancel
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp
import kotlin.collections.sumByDouble
import kotlin.let
import kotlin.math.abs
import kotlin.text.contains
import kotlin.text.dropLast
import kotlin.text.isEmpty
import kotlin.text.isNotEmpty
import kotlin.text.toDouble

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CashPaymentScreen(
    order: Order,
    onDismiss: () -> Unit,
    onPaymentComplete: (Order) -> Unit,
    viewModel: PaymentViewModel
) {
    CashPaymentDialogContent(
        order = order,
        onDismiss = onDismiss,
        onPaymentComplete = onPaymentComplete,
        viewModel = viewModel
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CashPaymentDialogContent(
    order: Order,
    onDismiss: () -> Unit,
    onPaymentComplete: (Order) -> Unit,
    viewModel: PaymentViewModel
){
    val orderState by viewModel.collectAsState(PaymentState::order)

    // Get the order object, fall back to the passed order if orderState isn't available
    val currentOrder = orderState()?.let { it } ?: order

    // Make sure we use the correct totalPrice by ensuring it's properly calculated
    val totalAmount = currentOrder.totalPrice ?: currentOrder.carts?.sumByDouble {
        (it.netPayable ?: 0.0) + (it.tax ?: 0.0)
    } ?: 0.0

    val amountGivenText by viewModel.collectAsState(PaymentState::amountGivenText)
    val showChangeCalculation by viewModel.collectAsState(PaymentState::showChangeCalculation)

    // Calculate change amount
    val changeAmount = try {
        if (amountGivenText.isNotEmpty()) {
            amountGivenText.toDouble() - totalAmount
        } else {
            0.0
        }
    } catch (e: NumberFormatException) {
        0.0
    }

    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color.White
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Header with DASA logo and hamburger menu
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
//                Text(
//                    text = "DASA",
//                    fontSize = 28.sp,
//                    fontWeight = FontWeight.Bold,
//                    color = Color(0xFF2E7D32)
//                )

                val headerImage = viewModel.prefs.loginResponse?.businesses?.headerImage
                val imageUrl = "${BASE_DOMAIN}/dasa/streamer?name=${headerImage}"
                val request: ImageRequest =
                    ImageRequest.Builder(LocalContext.current.applicationContext).data(imageUrl)
                        .crossfade(true).diskCacheKey(imageUrl).diskCachePolicy(CachePolicy.ENABLED)
                        .setHeader("Cache-Control", "max-age=31536000").build()
                AsyncImage(
                    modifier = Modifier.size(50.sdp), model = request, contentDescription = ""
                )
                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Close",
                        tint = Color(0xFF2E7D32)
                    )
                }
            }

            // Cash Payment Header
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFF2E7D32))
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Cash Payment",
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "£${totalAmount.transformDecimal()}",
                        color = Color.White,
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "Amount to pay",
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 14.sp
                    )
                }
            }

            // Amount Collected Section
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Amount collected",
                        color = Color.Black.copy(alpha = 0.7f),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    
                    // Display Amount Input
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .background(
                                Color.White,
                                RoundedCornerShape(8.dp)
                            )
                            .border(
                                1.dp,
                                Color.Gray.copy(alpha = 0.3f),
                                RoundedCornerShape(8.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = if (amountGivenText.isEmpty()) "Enter Amount Received" else "£${amountGivenText}",
                            color = if (amountGivenText.isEmpty()) Color.Gray else Color.Black,
                            fontSize = 18.sp,
                            fontWeight = if (amountGivenText.isEmpty()) FontWeight.Normal else FontWeight.Bold
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Return Change
                    Button(
                        onClick = { },
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.White,
                            contentColor = if (amountGivenText.isEmpty()) Color.Transparent else if (changeAmount < 0) Color(0xFFD32F2F) else Color(0xFF2E7D32)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = if (amountGivenText.isEmpty()) 
                                " " // White space when no amount entered
                            else if (changeAmount < 0) 
                                "Additional Required £${abs(changeAmount).transformDecimal()}"
                            else 
                                "Return Change £${changeAmount.transformDecimal()}",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = if (amountGivenText.isEmpty()) Color.Transparent else if (changeAmount < 0) Color(0xFFD32F2F) else Color(0xFF2E7D32)
                        )
                    }
                }
            }

            // Custom Number Pad
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFF2E7D32))
            ) {
                NumberPad(
                onNumberClick = { number ->
                    val newAmount = amountGivenText + number
                    // Check if the new amount would exceed 100,000
                    try {
                        val numericValue = newAmount.toDouble()
                        if (numericValue <= 100000.0) {
                            viewModel.updateAmountGivenText(newAmount)
                            if (newAmount.isNotEmpty()) {
                                viewModel.showChangeCalculation(true)
                            }
                        }
                        // If it exceeds 100,000, ignore the input
                    } catch (e: NumberFormatException) {
                        // If it's not a valid number, allow it for now (might be in the middle of typing)
                        if (newAmount.length <= 10) { // Reasonable length limit
                            viewModel.updateAmountGivenText(newAmount)
                            if (newAmount.isNotEmpty()) {
                                viewModel.showChangeCalculation(true)
                            }
                        }
                    }
                },
                onDecimalClick = {
                    if (!amountGivenText.contains(".")) {
                        val newAmount = if (amountGivenText.isEmpty()) "0." else "$amountGivenText."
                        // Check if the amount with decimal would exceed 100,000
                        try {
                            val numericValue = newAmount.toDouble()
                            if (numericValue <= 100000.0) {
                                viewModel.updateAmountGivenText(newAmount)
                                viewModel.showChangeCalculation(true)
                            }
                        } catch (e: NumberFormatException) {
                            // Allow decimal if it's still reasonable length
                            if (newAmount.length <= 10) {
                                viewModel.updateAmountGivenText(newAmount)
                                viewModel.showChangeCalculation(true)
                            }
                        }
                    }
                },
                onBackspaceClick = {
                    val newAmount = if (amountGivenText.isNotEmpty()) {
                        amountGivenText.dropLast(1)
                    } else {
                        ""
                    }
                    viewModel.updateAmountGivenText(newAmount)
                    viewModel.showChangeCalculation(newAmount.isNotEmpty())
                },
                onConfirmClick = {
                    val updatedOrder = currentOrder.copy(paymentType = 5)
                    onPaymentComplete(updatedOrder)
                    onDismiss()
                },
                isConfirmEnabled = changeAmount >= 0 && amountGivenText.isNotEmpty()
            )
            }
        }
    }
}

@Composable
private fun NumberPad(
    onNumberClick: (String) -> Unit,
    onDecimalClick: () -> Unit,
    onBackspaceClick: () -> Unit,
    onConfirmClick: () -> Unit,
    isConfirmEnabled: Boolean
) {
    Row(
        modifier = Modifier.padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Left side - Number pad
        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // Row 1: 1, 2, 3
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                NumberButton(
                    number = "1",
                    letters = "",
                    onClick = { onNumberClick("1") }
                )
                NumberButton(
                    number = "2",
                    letters = "",
                    onClick = { onNumberClick("2") }
                )
                NumberButton(
                    number = "3",
                    letters = "",
                    onClick = { onNumberClick("3") }
                )
            }

            // Row 2: 4, 5, 6
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                NumberButton(
                    number = "4",
                    letters = "",
                    onClick = { onNumberClick("4") }
                )
                NumberButton(
                    number = "5",
                    letters = "",
                    onClick = { onNumberClick("5") }
                )
                NumberButton(
                    number = "6",
                    letters = "",
                    onClick = { onNumberClick("6") }
                )
            }

            // Row 3: 7, 8, 9
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                NumberButton(
                    number = "7",
                    letters = "",
                    onClick = { onNumberClick("7") }
                )
                NumberButton(
                    number = "8",
                    letters = "",
                    onClick = { onNumberClick("8") }
                )
                NumberButton(
                    number = "9",
                    letters = "",
                    onClick = { onNumberClick("9") }
                )
            }

            // Row 4: Decimal, 0, Backspace
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Decimal button
                NumberButton(
                    number = ".",
                    letters = "",
                    onClick = { onDecimalClick() }
                )
                
                NumberButton(
                    number = "0",
                    letters = "",
                    onClick = { onNumberClick("0") }
                )
                
                // Backspace button
                Box(
                    modifier = Modifier
                        .size(width = 60.dp, height = 50.dp)
                        .background(
                            Color.White,
                            RoundedCornerShape(8.dp)
                        )
                        .border(
                            1.dp,
                            Color.Gray.copy(alpha = 0.2f),
                            RoundedCornerShape(8.dp)
                        )
                        .clickable { onBackspaceClick() },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Backspace,
                        contentDescription = "Backspace",
                        tint = Color.Black,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }

        // Right side - Confirm button (centered vertically)
        Box(
            modifier = Modifier
                .size(60.dp)
                .background(
                    Color.White,
                    CircleShape
                )
                .border(
                    2.dp,
                    if (isConfirmEnabled) Color(0xFF2E7D32) else Color.Gray,
                    CircleShape
                )
                .clickable(enabled = isConfirmEnabled) { 
                    if (isConfirmEnabled) onConfirmClick() 
                },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = "Confirm",
                tint = if (isConfirmEnabled) Color(0xFF2E7D32) else Color.Gray,
                modifier = Modifier.size(28.dp)
            )
        }
    }
}

@Composable
private fun NumberButton(
    number: String,
    letters: String,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(width = 60.dp, height = 50.dp)
            .background(
                Color.White,
                RoundedCornerShape(8.dp)
            )
            .border(
                1.dp,
                Color.Gray.copy(alpha = 0.2f),
                RoundedCornerShape(8.dp)
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = number,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            textAlign = TextAlign.Center
        )
    }
}