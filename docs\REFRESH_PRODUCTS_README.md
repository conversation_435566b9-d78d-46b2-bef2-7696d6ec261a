# Refresh Products Feature

## Overview
A refresh button has been added to the DasaProductsHeader component that allows users to update products from the API. The refresh functionality intelligently handles both updating existing products and inserting new ones. The button appears in both the Categories screen and Category Products screen.

## Key Features

### 🔄 Smart Refresh
- **Update Existing**: Products that already exist in the database are updated with latest information
- **Insert New**: New products from the API are inserted into the database
- **Upsert Logic**: Uses `insertOrReplaceProduct` for efficient data management

### 🎨 UI/UX
- **DasaProductsHeader**: Clean, modern header design with refresh button next to hamburger menu
- **Loading State**: Visual feedback during refresh with spinning indicator
- **Disabled State**: Button disabled during refresh to prevent multiple simultaneous calls
- **Material 3**: Follows Material Design 3 guidelines

### ⚡ Performance
- **Database First**: All product displays now load from local database (much faster)
- **API Sync**: Only refresh calls API to sync latest data
- **Background Processing**: Refresh operations run on IO dispatcher

## Implementation Details

### 1. Database Layer
```kotlin
// ProductRepository.kt
suspend fun insertMultipleProducts(stockItems: List<StockItem>): StateFlow<Async<Boolean>> {
    // Uses insertOrReplaceProduct for upsert functionality
}
```

### 2. Business Logic Layer
```kotlin
// DownloadProductsUseCase.kt
suspend fun refreshProducts(): StateFlow<Async<Boolean>> {
    // Fetches from API and saves to database
}
```

### 3. Presentation Layer
```kotlin
// ProductsScreenViewModel.kt
suspend fun refreshProducts(): StateFlow<Async<Boolean>> {
    // Manages UI state and triggers data refresh
}
```

### 4. UI Layer
```kotlin
// DasaProductsHeader in CategoryProductsScreen.kt
Row {
    IconButton(
        onClick = { viewModel.refreshProducts() },
        enabled = !refreshing
    ) {
        if (refreshing) CircularProgressIndicator()
        else Icon(Icons.Default.Refresh)
    }
}
```

## Data Flow

```
User Taps Refresh Button
        ↓
ProductsScreenViewModel.refreshProducts()
        ↓
DownloadProductsUseCase.refreshProducts()
        ↓
StockRepository.getPagedStockItems() [API Call]
        ↓
ProductRepository.insertMultipleProducts() [Database Upsert]
        ↓
StockUseCase() [Load Updated Data from Database]
        ↓
UI Updates with Refreshed Products
```

## Benefits

### 🚀 Performance
- **Instant Loading**: Products load immediately from database
- **Reduced API Calls**: Only refresh when needed
- **Offline Support**: Products available without internet after initial download

### 💾 Data Management
- **Always Up-to-Date**: Refresh ensures latest product information
- **Conflict Resolution**: Upsert handles data conflicts automatically
- **Incremental Updates**: Only changed data is updated

### 👤 User Experience
- **Visual Feedback**: Clear indication when refresh is in progress
- **Non-Blocking**: UI remains responsive during refresh
- **Intuitive**: Familiar refresh pattern users expect

## Usage

1. **Initial Setup**: Use Download Products screen for first-time product download
2. **Regular Use**: Products load instantly from database
3. **Refresh**: Tap refresh button in DasaProductsHeader (appears in both Categories and Category Products screens) to sync latest data from API
4. **Offline**: Products remain available even without internet

## Technical Architecture

### MvRx Pattern
- State management using MvRx for predictable UI updates
- Async state handling for loading, success, and error states

### Dependency Injection
- Hilt provides dependencies throughout the app
- AssistedInject for ViewModel factory creation

### Database
- SQLDelight for type-safe database operations
- Optimized queries for performance

### Coroutines
- Structured concurrency for background operations
- Proper dispatcher usage (IO for database/network)

## Future Enhancements

1. **Pull-to-Refresh**: Add swipe-to-refresh gesture support
2. **Auto-Refresh**: Periodic background refresh based on time
3. **Selective Refresh**: Refresh only specific categories
4. **Conflict Resolution**: Advanced merge strategies for data conflicts
5. **Offline Queue**: Queue refresh requests when offline

## Error Handling

- **Network Errors**: Graceful handling of API failures
- **Database Errors**: Fallback mechanisms for database issues
- **User Feedback**: Clear error messages when refresh fails

## Testing

- Unit tests for use cases and repository layers
- Integration tests for end-to-end refresh flow
- UI tests for refresh button interactions

---

This refresh functionality provides a robust, user-friendly way to keep product data synchronized while maintaining excellent performance through local database caching. 