package com.thedasagroup.suminative.ui.reservations

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.response.reservations.Area
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * Use case for fetching reservation areas for a store
 * 
 * This use case handles the business logic for retrieving areas
 * where tables can be reserved in a restaurant/store.
 */
class GetReservationAreasUseCase @Inject constructor(
    private val reservationsRepository: ReservationsRepository,
    private val prefs: Prefs
) {
    /**
     * Fetch reservation areas for the current store
     * 
     * @param storeId Optional store ID. If null, uses the current store from preferences
     * @return StateFlow containing the async result with list of areas
     */
    suspend operator fun invoke(
        storeId: Int? = null
    ): StateFlow<Async<List<Area>>> {
        val finalStoreId = storeId ?: prefs.store?.id ?: 0
        return reservationsRepository.getReservationAreas(finalStoreId)
    }
}
