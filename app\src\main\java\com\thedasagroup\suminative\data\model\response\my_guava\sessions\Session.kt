package com.thedasagroup.suminative.data.model.response.my_guava.sessions

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Session(
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("expire_at")
    val expireAt: String? = null,
    @SerialName("id")
    val id: String? = null,
    @SerialName("order_id")
    val orderId: String? = null,
    @SerialName("status")
    val status: String? = null,
    @SerialName("terminal_id")
    val terminalId: String? = null,
    @SerialName("updated_at")
    val updatedAt: String? = null,
    val messageForUser : String? = null,
)