package com.thedasagroup.suminative.ui.guava_orders

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.compose.mavericksViewModel
import com.google.accompanist.swiperefresh.SwipeRefresh
import com.google.accompanist.swiperefresh.rememberSwipeRefreshState
import com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder
import com.thedasagroup.suminative.ui.refund.RefundDialogHelper
import com.thedasagroup.suminative.ui.theme.fontNunito
import com.thedasagroup.suminative.ui.utils.transformDecimal
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GuavaOrdersScreen(
    viewModel: GuavaOrdersViewModel,
    onBackPressed: () -> Unit = {},
    activity: GuavaOrdersActivity
) {
    val ordersResponse by viewModel.collectAsState(GuavaOrdersState::ordersResponse)
    val refreshing by viewModel.collectAsState(GuavaOrdersState::refreshing)
    val coroutineScope = rememberCoroutineScope()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Guava Orders") },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        SwipeRefresh(
            state = rememberSwipeRefreshState(isRefreshing = refreshing),
            onRefresh = {
                coroutineScope.launch(Dispatchers.IO) {
                    viewModel.fetchOrders()
                }
            },
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            Box(modifier = Modifier.fillMaxSize()) {
                when (ordersResponse) {
                    is Loading -> {
                        CircularProgressIndicator(modifier = Modifier.align(Alignment.Center))
                    }

                    is Success -> {
                        val orders = ordersResponse()?.data?.list ?: mutableListOf()
                        if (orders.isEmpty()) {
                            Text(
                                text = "No orders found",
                                modifier = Modifier.align(Alignment.Center)
                            )
                        } else {
                            OrdersList(orders = orders, viewModel = viewModel, activity = activity)
                        }
                    }

                    is Fail -> {
                        val error = (ordersResponse as Fail<*>).error.message ?: "Unknown error"
                        Column(
                            modifier = Modifier.align(Alignment.Center),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "Error: $error",
                                color = Color.Red
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Button(onClick = {
                                coroutineScope.launch(Dispatchers.IO) {
                                    viewModel.fetchOrders()
                                }
                            }) {
                                Text("Retry")
                            }
                        }
                    }

                    else -> {
                        // Initial state, do nothing
                    }
                }
            }
        }
    }
}

@Composable
fun OrdersList(orders: List<GuavaOrder>, viewModel: GuavaOrdersViewModel,
               activity: GuavaOrdersActivity) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(orders) { order ->
            OrderCard(order = order, viewModel = viewModel, activity = activity)
        }
    }
}

@Composable
fun OrderCard(order: GuavaOrder, viewModel: GuavaOrdersViewModel,
              activity: GuavaOrdersActivity) {
    val coroutineScope = rememberCoroutineScope()
    var showRefundOptions by remember { mutableStateOf(false) }
    val voidResponse by viewModel.collectAsState(GuavaOrdersState::voidOrderResponse)

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
        ) {
            // Order ID and Status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Order #${order.id}",
                    fontFamily = fontNunito,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
                StatusChip(status = order.status ?: "Unknown")
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Date
            Text(
                text = "Created: ${formatDate(order.createdAt)}",
                fontFamily = fontNunito,
                fontSize = 14.sp,
                color = Color.Gray
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Amount
            Text(
                text = "Amount: £${order.totalAmount?.toDouble()?.transformDecimal() ?: "0.00"}",
                fontFamily = fontNunito,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            if (voidResponse is Loading) {

            } else {
                // Refund Options
                if (order.status?.lowercase() == "authorized") {
                    if (!showRefundOptions) {
                        Button(
                            onClick = { showRefundOptions = true },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text("Refund")
                        }
                    } else {
                        Column(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Button(
                                onClick = {
                                    RefundDialogHelper.showPaymentDialog(
                                        activity = activity,
                                        guavaOrder = order,
                                        onPaymentSuccess = {
                                            coroutineScope.launch {
                                                viewModel.fetchOrders()
                                            }
                                        }
                                    )
                                    showRefundOptions = false
                                },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Text("Refund using Card")
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            Button(
                                onClick = {
                                    coroutineScope.launch {
                                        viewModel.voidOrder(
                                            orderId = order.id ?: "",
                                            amount = order.totalAmount ?: "0.00"
                                        )
                                    }
                                    showRefundOptions = false
                                },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Text("Refund no card")
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            TextButton(
                                onClick = { showRefundOptions = false },
                                modifier = Modifier.align(Alignment.End)
                            ) {
                                Text("Cancel")
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun StatusChip(status: String) {
    val statusColor = when (status.lowercase()) {
        "pending" -> Color(0xFFFFA000) // Amber
        "completed" -> Color(0xFF4CAF50) // Green
        "cancelled" -> Color(0xFFF44336) // Red
        "authorized" -> Color(0xFF2196F3) // Blue
        else -> Color.Gray
    }

    Surface(
        color = statusColor.copy(alpha = 0.1f),
        shape = MaterialTheme.shapes.small
    ) {
        Text(
            text = status,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            color = statusColor,
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold
        )
    }
}

fun formatDate(dateString: String?): String {
    if (dateString.isNullOrEmpty()) return "Unknown"

    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
        inputFormat.timeZone = TimeZone.getTimeZone("UTC")

        val outputFormat = SimpleDateFormat("dd MMM yyyy, HH:mm", Locale.getDefault())
        outputFormat.timeZone = TimeZone.getDefault()

        val date = inputFormat.parse(dateString)
        date?.let { outputFormat.format(it) } ?: "Unknown"
    } catch (e: Exception) {
        "Unknown"
    }
} 