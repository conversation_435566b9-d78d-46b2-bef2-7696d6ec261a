package com.thedasagroup.suminative.ui.categories

import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.payment.CashPaymentActivity
import com.thedasagroup.suminative.ui.payment.CashPaymentDialogFragment

/**
 * Helper object to show the Cash Payment dialog using Compose
 */
object CashPaymentCompose {

    /**
     * Show cash payment as full screen activity
     */
    fun showCashPaymentActivity(
        activity: FragmentActivity,
        order: Order,
        launcher: ActivityResultLauncher<Intent>
    ) {
        val intent = CashPaymentActivity.createIntent(activity, order)
        launcher.launch(intent)
    }

    fun showCashPaymentDialog(
        activity: FragmentActivity,
        order: Order,
        onPaymentComplete: (Order) -> Unit,
    ) {
        val dialogFragment = CashPaymentDialogFragment().newInstance(order, onPaymentComplete = onPaymentComplete)
        val fragmentManager: FragmentManager = activity.supportFragmentManager
        dialogFragment.show(fragmentManager, "dialog")
    }
}