package com.thedasagroup.suminative.ui.reservations

import android.annotation.SuppressLint
import com.airbnb.mvrx.Async
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_APP
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_RESERVATIONS
import kotlinx.coroutines.flow.StateFlow
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.TimeZone
import javax.inject.Inject

class GetActiveReservationsUseCase @Inject constructor(
    private val reservationsRepository: ReservationsRepository,
    private val prefs: Prefs,
    private val trueTimeImpl: TrueTimeImpl
) {
    @SuppressLint("SimpleDateFormat")
    suspend operator fun invoke(): StateFlow<Async<ReservationsResponse>> {
        val storeId = prefs.store?.id ?: 0
        val now = trueTimeImpl.now()
        val currentTime = SimpleDateFormat(DATE_FORMAT_RESERVATIONS).format(now)
//        val currentTime = "2025-07-18T00:18:55"
        return reservationsRepository.getActiveReservationsRetrofit(storeId = storeId, currentTime = currentTime,
            timezoneOffset = getDeviceTimezoneOffset())
    }

    /**
     * Gets the device's current timezone offset in minutes east of UTC
     * @return Timezone offset in minutes (e.g., +5h = 300, -3h = -180)
     */
    private fun getDeviceTimezoneOffset(): Int {
        val timeZone = TimeZone.getDefault()
        return timeZone.rawOffset / (1000 * 60) // Convert milliseconds to minutes
    }
}
