package com.thedasagroup.suminative.ui.rewards

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.thedasagroup.suminative.data.model.response.rewards.AddPointsResponse
import com.thedasagroup.suminative.data.model.response.rewards.GetAllCustomersResponse

data class RewardsState(
    val getAllCustomersResponse: Async<GetAllCustomersResponse> = Uninitialized,
    val addPointsResponse: Async<AddPointsResponse> = Uninitialized,
    val isLoading: Boolean = false,
    val selectedCustomerId: Int? = null,
    val selectedBusinessId: Int? = null
) : MavericksState
