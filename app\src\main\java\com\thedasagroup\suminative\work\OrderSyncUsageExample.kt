package com.thedasagroup.suminative.work

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Example ViewModel showing how to use the OrderSyncManager
 * This can be integrated into your existing ViewModels
 */
class OrderSyncViewModel @Inject constructor(
    private val orderSyncManager: OrderSyncManager
) : ViewModel() {

    private val _syncStatus = MutableStateFlow<SyncStatus>(SyncStatus.Idle)
    val syncStatus: StateFlow<SyncStatus> = _syncStatus.asStateFlow()

    init {
        // Start monitoring sync status
        monitorSyncStatus()
        
        // Schedule periodic sync when ViewModel is created
        // You might want to do this in Application.onCreate() instead
        schedulePeriodicSync()
    }

    /**
     * Trigger manual sync (e.g., when user presses a sync button)
     */
    fun triggerManualSync() {
        viewModelScope.launch {
            _syncStatus.value = SyncStatus.Syncing
            orderSyncManager.triggerImmediateSync()
        }
    }

    /**
     * Schedule periodic sync with custom interval
     */
    fun schedulePeriodicSync(intervalMinutes: Long = 30L) {
        orderSyncManager.schedulePeriodicSync(intervalMinutes)
    }

    /**
     * Cancel periodic sync
     */
    fun cancelPeriodicSync() {
        orderSyncManager.cancelPeriodicSync()
    }

    /**
     * Monitor sync status and update UI state
     */
    private fun monitorSyncStatus() {
        viewModelScope.launch {
            orderSyncManager.getLastSyncResult().collect { result ->
                _syncStatus.value = when {
                    result == null -> SyncStatus.Idle
                    result.isSuccess -> SyncStatus.Success(
                        syncedCount = result.syncedCount,
                        totalCount = result.totalCount
                    )
                    else -> SyncStatus.Error(
                        errorMessage = result.errorMessage ?: "Unknown error",
                        failedCount = result.failedCount,
                        totalCount = result.totalCount
                    )
                }
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        // Optionally cancel all sync work when ViewModel is cleared
        // orderSyncManager.cancelAllSync()
    }
}

/**
 * Sealed class representing different sync states
 */
sealed class SyncStatus {
    object Idle : SyncStatus()
    object Syncing : SyncStatus()
    data class Success(val syncedCount: Int, val totalCount: Int) : SyncStatus()
    data class Error(val errorMessage: String, val failedCount: Int, val totalCount: Int) : SyncStatus()
}

/**
 * Example usage in Application class or MainActivity
 * 
 * class MyApplication : Application() {
 *     @Inject lateinit var orderSyncManager: OrderSyncManager
 *     
 *     override fun onCreate() {
 *         super.onCreate()
 *         
 *         // Schedule periodic order sync
 *         orderSyncManager.schedulePeriodicSync(intervalMinutes = 30L)
 *     }
 * }
 * 
 * // In your Activity or Fragment:
 * class MainActivity : ComponentActivity() {
 *     @Inject lateinit var orderSyncManager: OrderSyncManager
 *     
 *     private fun setupSyncButton() {
 *         syncButton.setOnClickListener {
 *             // Trigger immediate sync
 *             orderSyncManager.triggerImmediateSync()
 *         }
 *     }
 *     
 *     private fun observeSyncStatus() {
 *         lifecycleScope.launch {
 *             orderSyncManager.isManualSyncRunning().collect { isRunning ->
 *                 syncButton.isEnabled = !isRunning
 *                 if (isRunning) {
 *                     // Show loading indicator
 *                 }
 *             }
 *         }
 *     }
 * }
 */ 