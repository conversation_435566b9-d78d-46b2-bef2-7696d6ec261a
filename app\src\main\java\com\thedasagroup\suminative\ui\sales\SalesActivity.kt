package com.thedasagroup.suminative.ui.sales

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.ui.printOrderBitmap
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import kotlinx.coroutines.launch

class SalesActivity : ComponentActivity(), MavericksView {
    val viewModel: ProductsScreenViewModel by viewModel()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SalesScreenMain(viewModel = viewModel, onBackClick = {
                finish()
            }, onPrintBill = { bitmap ->
                printOrderBitmap(bitmap, this)
            })
        }
    }

    override fun onResume() {
        super.onResume()
        lifecycleScope.launch {
            viewModel.getStockItems()
        }
    }

    override fun invalidate() {

    }
}