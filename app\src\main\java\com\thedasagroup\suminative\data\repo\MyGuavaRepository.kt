package com.thedasagroup.suminative.data.repo

import android.util.Log
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.instacart.truetime.time.TrueTimeImpl
import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import com.thedasagroup.suminative.App.Companion.GUAVA_TEST_API_KEY
import com.thedasagroup.suminative.App.Companion.GUAVA_URL_KEY
import com.thedasagroup.suminative.App.Companion.USE_GUAVA_TEST
import com.thedasagroup.suminative.BuildConfig
import com.thedasagroup.suminative.data.api.GuavaOrderService
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.my_guava.AmountBody
import com.thedasagroup.suminative.data.model.request.my_guava.orders.CreateOrderRequest
import com.thedasagroup.suminative.data.model.request.my_guava.orders.GetListOfOrdersRequest
import com.thedasagroup.suminative.data.model.request.my_guava.sessions.CreateSessionRequest
import com.thedasagroup.suminative.data.model.response.my_guava.failure.GuavaFailResponse
import com.thedasagroup.suminative.data.model.response.my_guava.failure.GuavaFailResponse2
import com.thedasagroup.suminative.data.model.response.my_guava.failure.GuavaFailResponseSingleMessage
import com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse
import com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GetListOfOrdersResponse
import com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder
import com.thedasagroup.suminative.data.model.response.my_guava.orders.updateorder.UpdateOrderRequest
import com.thedasagroup.suminative.data.model.response.my_guava.sessions.GuavaSessionResponse
import com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session
import com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse
import com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_DATE_ONLY
import com.thedasagroup.suminative.ui.utils.formatDate
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import timber.log.Timber


class MyGuavaRepository(private val trueTimeImpl: TrueTimeImpl, private val prefs: Prefs) :
    BaseRepository() {
    val testApiKey = "8SaAyahCRzCs8c3V1qa8wg==.aa54ebef5d778782781d5821c0f9d3ba"

    suspend fun createOrder(request: CreateOrderRequest): StateFlow<Async<GuavaOrderResponse>> {
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val GUAVA_EPOS = "${baseUrl}/epos"
        val date = trueTimeImpl.now().formatDate(DATE_FORMAT_DATE_ONLY)
        val flow = MutableStateFlow<Async<GuavaOrderResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = "$GUAVA_EPOS/orders") {
                    contentType(ContentType.Application.Json)
                    header("x-client-key", apiKey)
                    setBody(request)
                }
                return@safeApiCall when (response.status.value) {
                    in 200..299 -> {
                        Success(response.body<GuavaOrderResponse>())
                    }

                    else -> {
                        Fail(
                            error = Throwable(
                                response.body<GuavaFailResponse>().message ?: "Unknown error"
                            )
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    suspend fun voidOrder(orderId : String, amount : String): StateFlow<Async<GuavaOrderResponse>> {
        val amountBody = AmountBody(
            amount = amount
        )
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val GUAVA_EPOS = "${baseUrl}/epos"
        val flow = MutableStateFlow<Async<GuavaOrderResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.put(urlString = "$GUAVA_EPOS/orders/${orderId}/void") {
                    contentType(ContentType.Application.Json)
                    header("x-client-key", apiKey)
                    setBody(amountBody)
                }
                return@safeApiCall when (response.status.value) {
                    in 200..299 -> {
                        Success(response.body<GuavaOrderResponse>())
                    }
                    else -> {
                        Fail(
                            error = Throwable(
                                response.body<GuavaFailResponse>().message ?: "Unknown error"
                            )
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getListOfOrders(request: GetListOfOrdersRequest): StateFlow<Async<GetListOfOrdersResponse>> {
        val date = trueTimeImpl.now().formatDate(DATE_FORMAT_DATE_ONLY)
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val GUAVA_EPOS = "${baseUrl}/epos"
        val flow = MutableStateFlow<Async<GetListOfOrdersResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val baseUrl = "$GUAVA_EPOS/orders"
                val queryParams =
                    "?size=${request.size}&page=${request.page}&dateTo=${request.dateTo}"
                val fullUrl = baseUrl + queryParams

                val response = apiClient.get(urlString = fullUrl) {
                    contentType(ContentType.Application.Json)
                    header("x-client-key", apiKey)
                }
                return@safeApiCall when (response.status.value) {
                    in 200..299 -> {
                        Success(response.body<GetListOfOrdersResponse>())
                    }

                    else -> {
                        Fail(
                            error = Throwable(
                                response.body<GuavaFailResponse2>().message?.firstOrNull()
                                    ?: "Unknown error"
                            )
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getListOfOrders2(request: GetListOfOrdersRequest): StateFlow<Async<GetListOfOrdersResponse>> {
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val networkJson = Json { ignoreUnknownKeys = true }
        val logging = HttpLoggingInterceptor(object : HttpLoggingInterceptor.Logger {
            override fun log(message: String) {
                Log.v("Okhttp", message)
                Timber.tag("Okhttp").v(message = message)
            }
        })
        val client: OkHttpClient = OkHttpClient.Builder()
            .addInterceptor(logging)
            .build()
        val retrofit = Retrofit.Builder()
            .baseUrl("$baseUrl/")
            .client(client)
            .addConverterFactory(networkJson.asConverterFactory("application/json".toMediaType()))
            .build()
        val service: GuavaOrderService = retrofit.create(GuavaOrderService::class.java)
        val flow = MutableStateFlow<Async<GetListOfOrdersResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = service.listOrders(
                    size = request.size ?: 0,
                    page = request.page ?: 0,
                    dateTo = request.dateTo ?: "",
                    authKey = apiKey
                )
                return@safeApiCall if (response.isSuccessful) {
                    Success(response.body() ?: GetListOfOrdersResponse())
                } else {
                    Fail(
                        error = Throwable(
                            response.errorBody()?.string() ?: "Unknown error"
                        )
                    )
                }
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getOrderById(orderId: String): StateFlow<Async<GuavaOrder>> {
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val GUAVA_EPOS = "${baseUrl}/epos"
        val date = trueTimeImpl.now().formatDate(DATE_FORMAT_DATE_ONLY)
        val flow = MutableStateFlow<Async<GuavaOrder>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = "$GUAVA_EPOS/$orderId") {
                    contentType(ContentType.Application.Json)
                    header("x-client-key", apiKey)
                }
                return@safeApiCall when (response.status.value) {
                    in 200..299 -> {
                        Success(response.body<GuavaOrder>())
                    }

                    else -> {
                        Fail(
                            error = Throwable(
                                response.body<GuavaFailResponse>().message ?: "Unknown error"
                            )
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    suspend fun cancelOrder(orderId: String): StateFlow<Async<GuavaOrder>> {
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val GUAVA_EPOS = "${baseUrl}/epos"
        val date = trueTimeImpl.now().formatDate(DATE_FORMAT_DATE_ONLY)
        val flow = MutableStateFlow<Async<GuavaOrder>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.put(urlString = "$GUAVA_EPOS/$orderId/cancel") {
                    contentType(ContentType.Application.Json)
                    header("x-client-key", apiKey)
                }
                return@safeApiCall when (response.status.value) {
                    in 200..299 -> {
                        Success(response.body<GuavaOrder>())
                    }

                    else -> {
                        Fail(
                            error = Throwable(
                                response.body<GuavaFailResponse>().message ?: "Unknown error"
                            )
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    //get terminal list
    suspend fun getTerminalList(size: Int, page: Int): StateFlow<Async<GetTerminalListResponse>> {
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val GUAVA_EPOS = "${baseUrl}/epos"
        val flow = MutableStateFlow<Async<GetTerminalListResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.get(urlString = "$GUAVA_EPOS/terminals") {
                    contentType(ContentType.Application.Json)
                    parameter("size", size)
                    parameter("page", page)
                    header("x-client-key", apiKey)
                }
                return@safeApiCall when (response.status.value) {
                    in 200..299 -> {
                        Success(response.body<GetTerminalListResponse>())
                    }

                    else -> {
                        Fail(
                            error = Throwable(
                                response.body<GuavaFailResponse>().message ?: "Unknown error"
                            )
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    //get terminal by id
    suspend fun getTerminalById(terminalId: String): StateFlow<Async<Terminal>> {
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val GUAVA_EPOS = "${baseUrl}/epos"
        val flow = MutableStateFlow<Async<Terminal>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.get(urlString = "$GUAVA_EPOS/terminals/$terminalId") {
                    contentType(ContentType.Application.Json)
                    header("x-client-key", apiKey)
                }
                return@safeApiCall when (response.status.value) {
                    in 200..299 -> {
                        Success(response.body<Terminal>())
                    }

                    else -> {
                        Fail(
                            error = Throwable(
                                response.body<GuavaFailResponse>().message ?: "Unknown error"
                            )
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    //create session
    suspend fun createSession(request: CreateSessionRequest): StateFlow<Async<Session>> {
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val GUAVA_EPOS = "${baseUrl}/epos"
        val flow = MutableStateFlow<Async<Session>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = "$GUAVA_EPOS/sessions") {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                    header("x-client-key", apiKey)
                }
                return@safeApiCall when (response.status.value) {
                    in 200..299 -> {
                        Success(response.body<GuavaSessionResponse>().data ?: Session())
                    }

                    else -> {
                        Fail(
                            error = Throwable(
                                response.body<GuavaFailResponseSingleMessage>().message
                                    ?: "Unknown error"
                            )
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    // cancel session
    suspend fun cancelSession(sessionId: String): StateFlow<Async<Session>> {
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val GUAVA_EPOS = "${baseUrl}/epos"
        val flow = MutableStateFlow<Async<Session>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response =
                    apiClient.put(urlString = "$GUAVA_EPOS/sessions/$sessionId/cancel") {
                        contentType(ContentType.Application.Json)
                        header("x-client-key", apiKey)
                    }
                return@safeApiCall when (response.status.value) {
                    in 200..299 -> {
                        Success(response.body<Session>())
                    }

                    else -> {
                        Fail(
                            error = Throwable(
                                response.body<GuavaFailResponse>().message ?: "Unknown error"
                            )
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    //get session by id
    suspend fun getSessionById(sessionId: String): StateFlow<Async<Session>> {
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val GUAVA_EPOS = "${baseUrl}/epos"
        val flow = MutableStateFlow<Async<Session>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.get(urlString = "$GUAVA_EPOS/sessions/$sessionId") {
                    contentType(ContentType.Application.Json)
                    header("x-client-key", apiKey)
                }
                return@safeApiCall when (response.status.value) {
                    in 200..299 -> {
                        Success(response.body<GuavaSessionResponse>().data ?: Session())
                    }

                    else -> {
                        Fail(
                            error = Throwable(
                                response.body<GuavaFailResponse>().message ?: "Unknown error"
                            )
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    //update order by id
    suspend fun updateOrderById(
        orderId: String,
        request: UpdateOrderRequest
    ): StateFlow<Async<GuavaOrder>> {
        val apiKey = getApiKey()
        val baseUrl = getBaseUrl()
        val GUAVA_EPOS = "${baseUrl}/epos"
        val flow = MutableStateFlow<Async<GuavaOrder>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.put(urlString = "$GUAVA_EPOS/$orderId") {
                    contentType(ContentType.Application.Json)
                    header("x-client-key", apiKey)
                    setBody(request)
                }
                return@safeApiCall when (response.status.value) {
                    in 200..299 -> {
                        Success(response.body<GuavaOrder>())
                    }

                    else -> {
                        Fail(
                            error = Throwable(
                                response.body<GuavaFailResponse>().message ?: "Unknown error"
                            )
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    fun getApiKey(): String {
        val useTest = if (BuildConfig.DEBUG) true else FirebaseRemoteConfig.getInstance()
            .getBoolean(USE_GUAVA_TEST)
        if (useTest) {
            return FirebaseRemoteConfig.getInstance().getString(GUAVA_TEST_API_KEY)
        }
        val storeId = prefs.store?.id
        return prefs.loginResponse?.stores?.firstOrNull { it.id == storeId }?.processorDetailsJson?.key
            ?: testApiKey
    }

    fun getBaseUrl(): String {
        val useTest = if (BuildConfig.DEBUG) true else FirebaseRemoteConfig.getInstance()
            .getBoolean(USE_GUAVA_TEST)
        return if (useTest) {
            "https://backoffice-api.guavapay.com/v1"
        } else {
            FirebaseRemoteConfig.getInstance().getString(GUAVA_URL_KEY)
        }
    }
}
