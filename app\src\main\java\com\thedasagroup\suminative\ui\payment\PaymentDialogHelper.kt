package com.thedasagroup.suminative.ui.payment

import androidx.fragment.app.FragmentActivity
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.store_orders.Order2

/**
 * Helper object to show the Payment dialog
 */
object PaymentDialogHelper {
    
    /**
     * Shows the payment dialog for a given order
     * 
     * @param activity The FragmentActivity where the dialog should be shown
     * @param order The order to process payment for, or null for a new order
     * @return The PaymentFragment instance that was shown
     */
    fun showPaymentDialog(activity: FragmentActivity, order: Order? = null, onPaymentSuccess : (Order2) -> Unit): PaymentFragment {
        val paymentFragment = PaymentFragment.newInstance(order, onPaymentSuccess = onPaymentSuccess)
        paymentFragment.show(
            activity.supportFragmentManager,
            PaymentFragment.TAG
        )
        return paymentFragment
    }
    
    /**
     * Dismisses any currently showing payment dialog
     * 
     * @param activity The FragmentActivity where the dialog was shown
     */
    fun dismissPaymentDialog(activity: FragmentActivity) {
        val fragment = activity.supportFragmentManager.findFragmentByTag(PaymentFragment.TAG)
        if (fragment is PaymentFragment) {
            fragment.dismiss()
        }
    }
} 