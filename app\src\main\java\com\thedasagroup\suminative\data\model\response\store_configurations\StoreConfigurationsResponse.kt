package com.thedasagroup.suminative.data.model.response.store_configurations

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class StoreConfigurationsResponse(
    @SerialName("success")
    val success: Boolean? = false,
    @SerialName("message")
    val message: String? = null,
    @SerialName("data")
    val data: StoreConfigurationData? = null,
    @SerialName("statusCode")
    val statusCode: Int? = 0
)

@Serializable
data class StoreConfigurationData(
    @SerialName("id")
    val id: Int? = 0,
    @SerialName("businessId")
    val businessId: Int? = 0,
    @SerialName("storeId")
    val storeId: Int? = 0,
    @SerialName("vatNumber")
    val vatNumber: String? = null,
    @SerialName("posEnabled")
    val posEnabled: Boolean? = false,
    @SerialName("mobileAppsEnabled")
    val mobileAppsEnabled: Boolean? = false,
    @SerialName("cateringEnabled")
    val cateringEnabled: Boolean? = false,
    @SerialName("reservationsEnabled")
    val reservationsEnabled: Boolean? = false,
    @SerialName("loyaltyEnabled")
    val loyaltyEnabled: Boolean? = false,
    @SerialName("quickService")
    val quickService: Boolean? = false,
    @SerialName("hoursTracking")
    val hoursTracking: Boolean? = false,
    @SerialName("createdBy")
    val createdBy: Int? = 0,
    @SerialName("createdOn")
    val createdOn: String? = null,
    @SerialName("modifiedBy")
    val modifiedBy: Int? = 0,
    @SerialName("modifiedOn")
    val modifiedOn: String? = null
)
