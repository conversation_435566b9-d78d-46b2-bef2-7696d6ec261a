package com.thedasagroup.suminative.data.model.response.notification

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class NotificationResponse(
    @SerialName("id") val id: Int? = 0,
    @SerialName("deviceId") val deviceId: String? = null,
    @SerialName("storeId") val password: Int? = 0,
    @SerialName("orderId") val orderId: Int? = 0,
    @SerialName("type") val type: String? = null,
)