package com.thedasagroup.suminative.data.model.response.change_status

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChangeStatusResponse(
    @SerialName("command") val command: String? = null,
    @SerialName("orderStatusHistories") val orderStatusHistories: List<OrderStatusHistory>? = mutableListOf(),
    @SerialName("paymentId") val paymentId: Int? = null,
    @SerialName("paymentResponseJson") val paymentResponseJson: String? = null,
    @SerialName("socketId") val socketId: String? = null,
    @SerialName("success") val success: Boolean? = false
)