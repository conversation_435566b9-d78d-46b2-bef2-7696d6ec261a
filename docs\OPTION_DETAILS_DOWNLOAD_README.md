# Option Details Download Feature

## Overview
The download products functionality has been enhanced to automatically fetch and save option details for every product that has customizable options. This provides a complete offline experience where both products and their customization options are available locally.

## Key Features

### 🔄 Automatic Option Detection
- **Smart API Calls**: Calls option details API for every product during download
- **Conditional Saving**: Only saves option details if the product actually has options
- **Error Resilience**: Continues downloading other products if one option fetch fails

### 💾 Database Storage
- **OptionSet Table**: Stores option groups (e.g., "Size", "Extras")
- **Option Table**: Stores individual options (e.g., "Large", "Extra Cheese")
- **Relational Design**: Proper foreign key relationships between products, option sets, and options

### ⚡ Performance Optimization
- **Rate Limiting**: 100ms delay between API calls to avoid overwhelming the server
- **Batch Processing**: Processes all products in sequence but efficiently
- **Transaction Safety**: Uses database transactions for data integrity

## Database Schema

### OptionSetEntity Table
```sql
CREATE TABLE OptionSetEntity (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    optionSetId INTEGER, -- Remote server ID
    productId INTEGER NOT NULL, -- Link to ProductEntity.productId
    name TEXT, -- e.g., "Size", "Extras"
    condition INTEGER, -- 1 = select one, 2 = select any number
    customNumber INTEGER,
    displayOrder INTEGER,
    itemId INTEGER,
    status INTEGER,
    variantType INTEGER,
    storeId INTEGER NOT NULL,
    createdAt INTEGER NOT NULL,
    updatedAt INTEGER NOT NULL,
    synced INTEGER NOT NULL DEFAULT 0,
    FOREIGN KEY(productId) REFERENCES ProductEntity(productId)
);
```

### OptionEntity Table
```sql
CREATE TABLE OptionEntity (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    optionId INTEGER, -- Remote server ID
    optionSetId INTEGER NOT NULL, -- Link to OptionSetEntity.optionSetId
    productId INTEGER NOT NULL, -- Link to ProductEntity.productId
    name TEXT, -- e.g., "Large", "Extra Cheese"
    price REAL NOT NULL DEFAULT 0.0,
    displayOrder INTEGER,
    status INTEGER,
    quantity INTEGER DEFAULT 0,
    initialQuantity INTEGER DEFAULT 1,
    storeId INTEGER NOT NULL,
    createdAt INTEGER NOT NULL,
    updatedAt INTEGER NOT NULL,
    synced INTEGER NOT NULL DEFAULT 0,
    FOREIGN KEY(optionSetId) REFERENCES OptionSetEntity(optionSetId),
    FOREIGN KEY(productId) REFERENCES ProductEntity(productId)
);
```

## Implementation Details

### 1. Database Layer - OptionRepository
```kotlin
// OptionRepository.kt
class OptionRepository(private val databaseManager: DatabaseManager) {
    suspend fun saveOptionDetails(productId: Int, storeId: Int, optionDetails: OptionDetails)
    suspend fun getOptionsByProduct(productId: Int)
    suspend fun getOptionSetsByProduct(productId: Int)
    suspend fun hasOptions(productId: Int)
    suspend fun deleteOptionsByProduct(productId: Int)
}
```

### 2. Business Logic Layer - Enhanced DownloadProductsUseCase
```kotlin
// DownloadProductsUseCase.kt
class DownloadProductsUseCase(
    private val stockRepository: StockRepository,
    private val productRepository: ProductRepository,
    private val optionRepository: OptionRepository, // NEW
    private val prefs: Prefs
) {
    // Enhanced to fetch and save option details for each product
}
```

### 3. Data Flow
```
Download Products Request
        ↓
Fetch Products from API
        ↓
Save Products to Database
        ↓
For Each Product:
    ↓
    Fetch Option Details from API
    ↓
    If Has Options:
        ↓
        Save Option Sets to Database
        ↓
        Save Options to Database
        ↓
    Continue to Next Product
        ↓
Complete Download
```

## New Files Created

1. **`app/src/main/sqldelight/com/thedasagroup/suminative/database/OptionSet.sq`**
   - Database schema for option sets
   - 15+ optimized SQL queries
   - Proper indexing for performance

2. **`app/src/main/sqldelight/com/thedasagroup/suminative/database/Option.sq`**
   - Database schema for individual options
   - 15+ optimized SQL queries
   - Foreign key relationships

3. **`app/src/main/java/com/thedasagroup/suminative/data/repo/OptionRepository.kt`**
   - Repository pattern for option data access
   - CRUD operations for options and option sets
   - Extension functions for data conversion

## Enhanced Features

### 🔧 **Download Process**
- **Products + Options**: Downloads both products and their customization options
- **Upsert Logic**: Updates existing options or inserts new ones
- **Error Handling**: Graceful handling of API failures for individual products

### 📱 **User Experience**
- **Complete Offline**: Products with options work fully offline after download
- **Faster Loading**: Option details load instantly from database
- **Consistent Data**: Options always match the current product state

### 🏗️ **Architecture**
- **Repository Pattern**: Clean separation of concerns
- **Dependency Injection**: Proper DI setup with Hilt
- **Relational Design**: Normalized database structure

## Usage Flow

1. **Initial Download**: User downloads products using DownloadProductsScreen
2. **Automatic Options**: System automatically fetches option details for each product
3. **Database Storage**: Options saved locally with proper relationships
4. **Offline Usage**: Products with options work without internet connection
5. **Refresh Updates**: Refresh button updates both products and their options

## Benefits

### 🚀 **Performance**
- **Instant Options**: Option details load immediately from database
- **Reduced API Calls**: Only download options once, use cached data
- **Batch Efficiency**: Optimized batch processing of multiple products

### 💾 **Data Integrity**
- **Relational Consistency**: Proper foreign key relationships
- **Transaction Safety**: All-or-nothing database operations
- **Conflict Resolution**: Upsert handles data conflicts automatically

### 👤 **User Experience**
- **Seamless Options**: Product customization works offline
- **Fast Interactions**: No loading delays for option selection
- **Complete Features**: Full functionality without internet dependency

## Error Handling

- **Individual Failures**: If one product's options fail, others continue
- **API Timeouts**: Graceful handling of network issues
- **Database Errors**: Transaction rollback on database failures
- **User Feedback**: Clear indication of download progress and issues

## Future Enhancements

1. **Selective Option Download**: Download options only for specific categories
2. **Option Validation**: Validate option combinations and constraints
3. **Cache Expiry**: Implement option cache expiration policies
4. **Batch API**: Request multiple product options in single API call
5. **Background Sync**: Sync options in background after initial download

## Technical Architecture

### Database Design
- **Normalized Structure**: Third normal form for optimal storage
- **Indexing Strategy**: Optimized queries for common access patterns
- **Foreign Keys**: Maintains referential integrity

### Repository Pattern
- **Single Responsibility**: Each repository handles one data type
- **Consistent Interface**: Uniform API across all repositories
- **Error Handling**: Standardized error response patterns

### Dependency Injection
- **Hilt Integration**: Seamless integration with existing DI setup
- **Singleton Scope**: Efficient resource management
- **Module Organization**: Clean separation of concerns

---

This enhancement provides a complete offline experience where users can browse products, view all customization options, and place orders without requiring internet connectivity after the initial download. 