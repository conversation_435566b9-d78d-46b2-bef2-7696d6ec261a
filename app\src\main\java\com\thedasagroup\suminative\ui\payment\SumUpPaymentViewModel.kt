package com.thedasagroup.suminative.ui.payment

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Uninitialized
import com.sumup.merchant.reader.models.TransactionInfo
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.store_orders.Order2

data class SumUpPaymentState(
    val order: Order = Order(),
    val isLoggedIn: Boolean = false,
    val loginRequest: Async<Boolean> = Uninitialized,
    val paymentRequest: Async<TransactionInfo> = Uninitialized,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null,
    val shouldFinish: Boolean = false,
    val resultOrder: Order? = null
) : MavericksState

class SumUpPaymentViewModel(
    initialState: SumUpPaymentState
) : MavericksViewModel<SumUpPaymentState>(initialState) {

    init {
        try {
            android.util.Log.d("SumUpPaymentViewModel", "Initializing ViewModel")
            // Check if user is already logged in to SumUp
            checkLoginStatus()
        } catch (e: Exception) {
            android.util.Log.e("SumUpPaymentViewModel", "Error in init", e)
            setState { copy(errorMessage = "Error initializing SumUp: ${e.message}") }
        }
    }

    fun checkLoginStatus() {
        try {
            android.util.Log.d("SumUpPaymentViewModel", "Checking login status")
            val isLoggedIn = SumUpPaymentHelper.isLoggedIn()
            android.util.Log.d("SumUpPaymentViewModel", "Login status: $isLoggedIn")
            setState { copy(isLoggedIn = isLoggedIn) }
        } catch (e: Exception) {
            android.util.Log.e("SumUpPaymentViewModel", "Error checking login status", e)
            setState { copy(isLoggedIn = false, errorMessage = "Error checking login status: ${e.message}") }
        }
    }

    fun onLoginResult(success: Boolean, message: String) {
        setState {
            copy(
                isLoggedIn = success,
                isLoading = false,
                errorMessage = if (!success) message else null,
                successMessage = if (success) "Login successful" else null
            )
        }
    }

    fun onPaymentResult(success: Boolean, order: Order?, transactionInfo: TransactionInfo?, message: String) {
        setState {
            copy(
                isLoading = false,
                errorMessage = if (!success) message else null,
                successMessage = if (success) "Payment successful" else null,
                shouldFinish = success,
                resultOrder = order
            )
        }
    }

    fun startLogin() {
        setState { copy(isLoading = true, errorMessage = null) }
    }

    fun startPayment() {
        setState { copy(isLoading = true, errorMessage = null) }
    }

    fun clearMessages() {
        setState { copy(errorMessage = null, successMessage = null) }
    }

    fun setOrder(order: Order) {
        setState { copy(order = order) }
    }


}
