package com.thedasagroup.suminative.ui.products

import com.airbnb.mvrx.Async
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.order.Conversation
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.FeedbackComplain
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.OrderRequest
import com.thedasagroup.suminative.data.model.request.order.OrderStatus
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.request.order.PaymentData
import com.thedasagroup.suminative.data.model.request.order.PromoCodes
import com.thedasagroup.suminative.data.model.request.order.SupportDetail
import com.thedasagroup.suminative.data.model.response.order.OrderResponse2
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.formatDate
import kotlinx.coroutines.flow.StateFlow
import java.util.UUID

open class PlaceOnlineOrderUseCase(
    private val stockRepository: StockRepository,
    private val guavaRepository: MyGuavaRepository,
    private val prefs: Prefs,
    private val trueTimeImpl: TrueTimeImpl
) {
    suspend operator fun invoke(order: Order, transId : String): StateFlow<Async<OrderResponse2>> {
        val total = order.carts?.sumOf { it.netPayable ?: 0.0 } ?: 0.0
        val totalOptionsPrice = order.carts?.sumOf {
            val storeItem = it.storeItem
            val optionSets = storeItem?.optionSets
            val optionSetsSum = optionSets?.sumOf {optioSet ->
                val optionItems = optioSet.options
                optionItems.sumOf { optionItem ->
                    if(optionItem?.optionchecked == true) {
                        (optionItem.price ?: 0.0) * (optionItem.quantity?.toDouble() ?: 0.0)
                    }
                    else 0.0
                }
            }
            optionSetsSum ?: 0.0
        } ?: 0.0
        val updatedOrder = order.copy(
            id = -1,
            businessId = prefs.store?.businessId,
            storeId = prefs.store?.id,
            customerId = -1,
            totalPrice = total.toDouble(),
            totalExtraPrice = 0.0,
            totalDiscount = 0.0,
            netPayable = total.toDouble(),
            deliveryCharges = 0.0,
            deliveryAddress = DeliveryAddress(),
            status = 1,
            createdBy = prefs.selectedWaiter?.id ?: -1,
            deliveryType = 4,
            paymentType = order.paymentType,
            createdOn = trueTimeImpl.now().formatDate(DATE_FORMAT_BACK_END),
            orderStatusHistory = mutableListOf(),
            tax = order.tax ?: 0.0,
            modifiedBy = -1,
            modifiedOn = trueTimeImpl.now().formatDate(DATE_FORMAT_BACK_END),
            deliveryNote = "",
            customer = Customer(),
            transactionId = transId,
            paymentId = 1122,
            trackingUrl = "",
            pickupTime = "",
            pandaOrderId = "",
            pandaOrderDetail = PandaOrderDetail(),
            lastUpdatedFromPanda = "",
            totalOptionPrice = totalOptionsPrice,
            promoCode = "",
            discountONPromo = 0.0,
            isPromoCodeAvailed = false,
            priceBeforePromo = 0,
            priceAfterPromo = 0,
            scheduled = false,
            scheduledDateTime = "",
            guest = false
        )
        val orderRequest = OrderRequest(
            command = "addEditOrderOk",
            businessId = prefs.store?.businessId,
            storeId = prefs.store?.id,
            loggedUserId = prefs.loginResponse?.user?.id,
            customer = updatedOrder.customer,
            customerId = updatedOrder.customerId,
            deliveryAddress = updatedOrder.deliveryAddress,
            paymentData = PaymentData(),
            supportDetail = SupportDetail(),
            feedbackComplain = FeedbackComplain(),
            conversation = Conversation(),
            appType = "",
            email = prefs.loginResponse?.user?.email,
            orderStatus = OrderStatus(),
            orderId = -1,
            pandaOrderId = "",
            itemId = -1,
            phoneNumber = prefs.loginResponse?.user?.phone,
            priceBeforePromo = updatedOrder.priceBeforePromo,
            priceAfterPromo = updatedOrder.priceAfterPromo,
            cartSize = updatedOrder.carts?.size ?: 0,
            currentDate = trueTimeImpl.now().formatDate(DATE_FORMAT_BACK_END),
            promoCodes = PromoCodes(
                id = -1
            ),
            order = updatedOrder
        )
        val orderResponse = stockRepository.placeOrder(
            request = updatedOrder,
        )
        return orderResponse
    }
}