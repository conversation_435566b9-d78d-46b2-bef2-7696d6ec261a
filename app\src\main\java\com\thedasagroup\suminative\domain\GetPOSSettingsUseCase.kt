package com.thedasagroup.suminative.domain

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.store_settings.GetPosSettingsRequest
import com.thedasagroup.suminative.data.model.request.store_settings.StoreSettingsRequest
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.model.response.login.StoreSettings
import com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.LoginRepository
import kotlinx.coroutines.flow.StateFlow

open class GetPOSSettingsUseCase(private val loginRepository: LoginRepository, private val prefs : Prefs) {
    suspend operator fun invoke() : StateFlow<Async<StoreSettingsResponse>> {

        return loginRepository.getPOSSettings(
            storeSettingsRequest = GetPosSettingsRequest(
                storeId = prefs.store?.id,
                businessId = prefs.loginResponse?.businesses?.id ?: 0,
            )
        )
    }
}