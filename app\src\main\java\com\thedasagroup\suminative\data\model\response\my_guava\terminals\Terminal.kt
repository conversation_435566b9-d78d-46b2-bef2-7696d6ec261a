package com.thedasagroup.suminative.data.model.response.my_guava.terminals

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Terminal(
    @SerialName("id")
    val id: String? = null,
    @SerialName("available")
    val available: Boolean? = false,
    @SerialName("dbaAddress")
    val dbaAddress: String? = null,
    @SerialName("dbaName")
    val dbaName: String? = null,
    @SerialName("description")
    val description: String? = null,
    @SerialName("identifier")
    val identifier: String? = null,
    @SerialName("serialNumber")
    val serialNumber: String? = null
)