# name: Upload APK in Releases
# on:
#   push:
#   # Sequence of patterns matched against refs/tags
#     #tags:
#     #  - 'v*' # Push events to matching v*, i.e. v1.0, v20.15.10
#     # branches: 
#       # - main
#       # - dev

jobs:
  apk:
    name: Upload APK in Releases
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v1
      - name: set up JDK 1.8
        uses: actions/setup-java@v1
        with:
          java-version: 17
          distribution: "temurin"

      - name: Grant Permission to Execute
        run: chmod +x gradlew
        
      - name: Build Staging APK
        run: bash ./gradlew assembleStagingRelease --stacktrace
      
      - name: Upload Staging APK to Github Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: app-staging
          path: app/build/outputs/apk/staging/release/app-staging-release.apk

      - name: Build Production APK
        run: bash ./gradlew assembleProdRelease --stacktrace
      
      - name: Upload Prod APK to Github Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: app-prod
          path: app/build/outputs/apk/prod/release/app-prod-release.apk

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ****************************************
        with:
          tag_name: ${{ github.run_id }}
          release_name: Release ${{ github.run_id }}
          draft: false
          prerelease: false

      - name: Upload Staging APK to Release
        id: upload-release-asset-staging 
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ****************************************
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }} # This pulls from the CREATE RELEASE step above, referencing it's ID to get its outputs object, which include a `upload_url`. See this blog post for more info: https://jasonet.co/posts/new-features-of-github-actions/#passing-data-to-future-steps 
          asset_path: ./app/build/outputs/apk/staging/release/app-staging-release.apk
          asset_name: app-staging-release.apk
          asset_content_type: application/zip

      - name: Upload Prod APK to Release
        id: upload-release-asset-prod 
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ****************************************
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }} # This pulls from the CREATE RELEASE step above, referencing it's ID to get its outputs object, which include a `upload_url`. See this blog post for more info: https://jasonet.co/posts/new-features-of-github-actions/#passing-data-to-future-steps 
          asset_path: ./app/build/outputs/apk/prod/release/app-prod-release.apk
          asset_name: app-prod-release.apk
          asset_content_type: application/zip

#      - name: Distribute Staging to AppCenter
#        uses: wzieba/AppCenter-Github-Action@v1.3.2
#        with:
#          appName: sheerazam/dasa-app
#          token: 9a30e7b357e161e29b9bb6d59ca13b8d997d619f
#          group: dasa-staging
#          file: app/build/outputs/apk/staging/release/app-staging-release.apk
#          debug: false

#      - name: Distribute Prod to AppCenter
#        uses: wzieba/AppCenter-Github-Action@v1.3.2
#        with:
#          appName: sheerazam/dasa-app
#          token: 9a30e7b357e161e29b9bb6d59ca13b8d997d619f
#          group: dasa-prod
#          file: app/build/outputs/apk/prod/release/app-prod-release.apk
#          debug: false


      - name: Upload build to Box
        uses: benjaminejarrell/box-upload-gh-actions@v1.2
        id: BoxUpload
        with:
          box-client-sdk-config: > 
            {
              "boxAppSettings": {
                "clientID": "********************************",
                "clientSecret": "YM9mQJwJEt6tbHmsVTyYe5TgbOftCAxr",
                "appAuth": {
                  "publicKeyID": "16ypdu04",
                  "privateKey": "-----BEGIN ENCRYPTED PRIVATE KEY-----\nMIIFHDBOBgkqhkiG9w0BBQ0wQTApBgkqhkiG9w0BBQwwHAQIUgXlLLHdu9UCAggA\nMAwGCCqGSIb3DQIJBQAwFAYIKoZIhvcNAwcECG6IKE9LzfgBBIIEyPn8cLnNz9ZE\neWw/gTpxwfZWgK7znqjGkjixPnGiwERyBIBFuPtBGp1njnymrjX9NA4QzEgFs4zR\nyft6IamqhEc2Op8lEBWdpvubMW2x9Y/Ky4eB308Gw24st6SmiLISinEzGMIj1NEv\nvabR8pFM6an1bk7DdWD/ikhPUgp1Y8e0//TCB0UKP7D2aICfanICswS45CsJrtBv\nBmuVHkaYt9r+09hRpTHwCEd6ARhnbuuuxA97r0tKHJF4vDc4Ec0yRehp6IuJfmbN\n/4uG7eo4lAFVq5GEqTVYJR3pdlcHF/NGwdkleClHg4JDdFY5kHLYSU/C4Zphc1ZY\niwpbBwH87H+pSmRyGHVcjkyRBdotlaRcM5MbBd2LtzgNTPonY1MUl9rGWBSpQRKO\nBSCAn5cz8ZyPYrz+IF8T4HyrKjNAkjcb/7HuFujXSgen0dTRQp7wfc/PP42FK5jK\nw2yTFqHF6xIJK7jBbMN9YK6ZY5p0KLizpYVxtnbTaEVYLJ+sZGrOL86YW2ffBPvj\nnq+2ODyhha2ndAonAV3aIqbGoQJfF9eUgo+jNlEM7Izokkz2YMbkLCcSm1LEfmu1\nvEeyZFMllZegsVHYZmrG+363xXZJ/XkX25m0/ZaiMm96KuYmkPM6dokYilBEl1o2\nGJKObyAvqdFb0snrcypUU7+TOrbpHz7pRzTZzP2tMZa0BnUTpvIOzoA3xmwkdr01\nraTuAJP/uUSqdW9JHa4DXfwFqGff1JYp6yMqfeVkF2X4P+Px6LbVkttc3Wdgfo5s\nW3gfK+iEKH+h1ytCDC5xX5JBrhUmtVwVGmF9L6jwGoXDdzrga1RdfuW/G7GhUXYo\nHf9J04T8XI9NugNPsTSdtpUUQoeSy+50Ed8aRcCZTuoUBmQptKs0XH0UxjMABQqT\ntKCiClMCNy3/VivLmtKnGbXAqZEW+N5aFoXa/XjVkLNFhWcj5WwGiW37B/Bnzx6o\nPEgzv4mz0Tna4gdm4RvFoPvn3xCqMTfQCN+bsbgSobAEp7iJg101GRpAfOZWd0US\nxjOKjk2vLHe/YagNNkUnYhR1u8ktpMt6G/6fk7wm2sevvL+cDcthOjrqjVr1nlGH\nvPOEUL+vFaFe+fx20Exa1cQ+aofRwba09se60Ecn5j3mfl97hCCYLgcIOY96AlXB\nctQKJUFEe4lJ+3vQdoBiKF/DwqO7emeq9R9YpAuCmqYX+l3SjpQF3pIlvbQBjwib\ndxG2hMFa1Oc2KwPexaNvjFOgJhW73YFYc7yJ7mK+pnI9tSHKMzeTWO/MpKKDzX+u\nuvnOtmTeQg+OXvh7rdl8Qp9rWSyP5MB1JcbAxAN7J2Fgt41mea9k3gSXSsSFTRRr\nc7SazH+99+/vYLZZAXD8CmrZ/shSLvTSsb0zBUy7y2LAtXhys+kD/RbhopIWUZ/Q\n1qI/PprVGzR+UvlL8MmtXAFJHwcbiczIXI1y+h1F0q4bsLu0oZ7I4r2/hyMpTil8\nOQpBEZo8LdM/hAAYNBUTZ5ZOvItx9HjDbFY7LPVdmJ9B8iQOjhUm5G+Q+RkO7skP\nvYenWmZfOox85HbZRUrfHYIp/i3Eh/HSk3a7+m8VzbS4AFGUeVJGEjIszXTIeA6W\nmfFe5H8o4GshXlp5bLG+jw==\n-----END ENCRYPTED PRIVATE KEY-----\n",
                  "passphrase": "c13e75fe075da685bf0285f22c2a7377"
                }
              },
              "enterpriseID": "0"
            }
          box-folder-id: 285827079618
          file:  app/build/outputs/apk/prod/release/app-prod-release.apk
