package com.thedasagroup.suminative.ui.login

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.login.ClockOutUserTimeRequest
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.repo.ClockInOutRepository
import kotlinx.coroutines.flow.StateFlow

class ClockOutUserTimeUseCase(private val clockInOutRepository: ClockInOutRepository) {
    suspend operator fun invoke(userPin: String, storeId: Int): StateFlow<Async<LoginResponse>> {
        return clockInOutRepository.clockOutUserTime(
            ClockOutUserTimeRequest(
                userPin = userPin,
                storeId = storeId
            )
        )
    }
} 