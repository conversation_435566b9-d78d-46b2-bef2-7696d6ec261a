package com.thedasagroup.suminative.domain.orders

import com.thedasagroup.suminative.data.database.LocalOrderRepository
import com.thedasagroup.suminative.database.OrderEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class OrderSyncUseCase @Inject constructor(
    private val orderRepository: LocalOrderRepository
) {
    
    fun getUnsyncedOrders(): Flow<List<OrderEntity>> {
        return orderRepository.getUnsyncedOrdersFlow()
    }
    
    suspend fun getUnsyncedOrdersList(): List<OrderEntity> {
        return orderRepository.getUnsyncedOrders()
    }
    
    fun getSyncedOrders(): Flow<List<OrderEntity>> {
        return orderRepository.getSyncedOrdersFlow()
    }
    
    suspend fun getUnsyncedOrderCount(): Long {
        return orderRepository.getUnsyncedOrderCount()
    }
    
    suspend fun markOrderAsSynced(orderId: Long) {
        orderRepository.markOrderSynced(orderId)
    }
    
    suspend fun markOrderAsUnsynced(orderId: Long) {
        orderRepository.markOrderUnsynced(orderId)
    }
    
    suspend fun markMultipleOrdersAsSynced(orderIds: List<String>) {
        orderRepository.markOrdersSyncedByOrderIds(orderIds)
    }
    
    /**
     * Simulates syncing all unsynced orders to a remote server
     * In a real implementation, this would make API calls to sync with server
     */
    suspend fun syncAllUnsyncedOrders(): SyncResult {
        return try {
            val unsyncedOrders = getUnsyncedOrdersList()
            
            if (unsyncedOrders.isEmpty()) {
                return SyncResult.Success(0, "No orders to sync")
            }
            
            // TODO: In real implementation, make API calls here to sync with server
            // For now, we'll just mark them as synced
            val orderIds = unsyncedOrders.map { it.orderId }
            markMultipleOrdersAsSynced(orderIds)
            
            SyncResult.Success(
                syncedCount = unsyncedOrders.size,
                message = "Successfully synced ${unsyncedOrders.size} orders"
            )
        } catch (e: Exception) {
            SyncResult.Error(e.message ?: "Unknown sync error")
        }
    }
    
    /**
     * Force resync of a specific order (mark as unsynced first, then sync)
     */
    suspend fun forceSyncOrder(orderId: Long): SyncResult {
        return try {
            // Mark as unsynced first
            markOrderAsUnsynced(orderId)
            
            // TODO: Make API call to sync this specific order
            // For now, just mark as synced again
            markOrderAsSynced(orderId)
            
            SyncResult.Success(1, "Order synced successfully")
        } catch (e: Exception) {
            SyncResult.Error(e.message ?: "Failed to sync order")
        }
    }
}