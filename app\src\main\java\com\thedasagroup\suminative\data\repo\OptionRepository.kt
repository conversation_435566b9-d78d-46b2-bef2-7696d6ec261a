package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.database.DatabaseManager
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.request.order.OptionSet
import com.thedasagroup.suminative.data.model.response.store_orders.Option
import com.thedasagroup.suminative.database.OptionEntity
import com.thedasagroup.suminative.database.OptionSetEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class OptionRepository(
    private val databaseManager: DatabaseManager
) : BaseRepository() {

    private val optionQueries = databaseManager.database.optionQueries
    private val optionSetQueries = databaseManager.database.optionSetQueries

    suspend fun saveOptionDetails(productId: Int, storeId: Int, optionDetails: OptionDetails): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        
        withContext(Dispatchers.IO) {
            try {
                val currentTime = System.currentTimeMillis()
                
                databaseManager.database.transaction {
                    // First delete existing options for this product
                    optionQueries.deleteOptionsByProduct(productId.toLong())
                    optionSetQueries.deleteOptionSetsByProduct(productId.toLong())
                    
                    // Save option sets and options
                    optionDetails.optionSets?.forEach { optionSet ->
                        // Save option set
                        optionSetQueries.insertOrReplaceOptionSet(
                            optionSetId = optionSet.id?.toLong(),
                            productId = productId.toLong(),
                            name = optionSet.name,
                            condition = optionSet.condition?.toLong(),
                            customNumber = optionSet.customNumber?.toLong(),
                            displayOrder = optionSet.displayOrder?.toLong(),
                            itemId = optionSet.itemId?.toLong(),
                            status = optionSet.status?.toLong(),
                            variantType = optionSet.variantType?.toLong(),
                            storeId = storeId.toLong(),
                            createdAt = currentTime,
                            updatedAt = currentTime,
                            synced = 1L
                        )
                        
                        // Save options for this option set
                        optionSet.options.forEach { option ->
                            option?.let { opt ->
                                optionQueries.insertOrReplaceOption(
                                    optionId = opt.id?.toLong(),
                                    optionSetId = optionSet.id?.toLong() ?: 0,
                                    productId = productId.toLong(),
                                    name = opt.name,
                                    price = opt.price ?: 0.0,
                                    displayOrder = opt.displayOrder?.toLong(),
                                    status = opt.status?.toLong(),
                                    quantity = opt.quantity?.toLong() ?: 0L,
                                    initialQuantity = opt.initialQuantity?.toLong() ?: 1L,
                                    storeId = storeId.toLong(),
                                    createdAt = currentTime,
                                    updatedAt = currentTime,
                                    synced = 1L
                                )
                            }
                        }
                    }
                }
                
                flow.value = Success(true)
            } catch (e: Exception) {
                flow.value = Success(false)
            }
        }
        
        return flow
    }

    suspend fun getOptionsByProduct(productId: Int): StateFlow<Async<List<OptionEntity>>> {
        val flow = MutableStateFlow<Async<List<OptionEntity>>>(Loading())
        
        withContext(Dispatchers.IO) {
            try {
                val options = optionQueries.getOptionsByProduct(productId.toLong()).executeAsList()
                flow.value = Success(options)
            } catch (e: Exception) {
                flow.value = Success(emptyList())
            }
        }
        
        return flow
    }

    suspend fun getOptionSetsByProduct(productId: Int): StateFlow<Async<List<OptionSetEntity>>> {
        val flow = MutableStateFlow<Async<List<OptionSetEntity>>>(Loading())
        
        withContext(Dispatchers.IO) {
            try {
                val optionSets = optionSetQueries.getOptionSetsByProduct(productId.toLong()).executeAsList()
                flow.value = Success(optionSets)
            } catch (e: Exception) {
                flow.value = Success(emptyList())
            }
        }
        
        return flow
    }

    suspend fun hasOptions(productId: Int): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        
        withContext(Dispatchers.IO) {
            try {
                val count = optionSetQueries.countOptionSetsByProduct(productId.toLong()).executeAsOne()
                flow.value = Success(count > 0)
            } catch (e: Exception) {
                flow.value = Success(false)
            }
        }
        
        return flow
    }

    suspend fun deleteOptionsByProduct(productId: Int): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        
        withContext(Dispatchers.IO) {
            try {
                databaseManager.database.transaction {
                    optionQueries.deleteOptionsByProduct(productId.toLong())
                    optionSetQueries.deleteOptionSetsByProduct(productId.toLong())
                }
                flow.value = Success(true)
            } catch (e: Exception) {
                flow.value = Success(false)
            }
        }
        
        return flow
    }

    suspend fun getOptionDetailsFromDatabase(productId: Int): StateFlow<Async<OptionDetails>> {
        val flow = MutableStateFlow<Async<OptionDetails>>(Loading())
        
        withContext(Dispatchers.IO) {
            try {
                // Get all option sets for this product
                val optionSets = optionSetQueries.getOptionSetsByProduct(productId.toLong()).executeAsList()
                
                if (optionSets.isEmpty()) {
                    // Return empty option details if no options exist
                    flow.value = Success(OptionDetails(
                        command = "getOptionSetByItemIdOk",
                        optionSets = emptyList(),
                        success = true
                    ))
                    return@withContext
                }
                
                // Convert database entities to API models
                val convertedOptionSets = optionSets.map { optionSetEntity ->
                    // Get options for this option set
                    val options = optionQueries.getOptionsByOptionSet(optionSetEntity.optionSetId ?: 0).executeAsList()
                    
                    // Convert to API model
                    optionSetEntity.toOptionSet(options)
                }
                
                // Create OptionDetails response
                val optionDetails = OptionDetails(
                    command = "getOptionSetByItemIdOk",
                    optionSets = convertedOptionSets,
                    paymentId = 0,
                    paymentResponseJson = null,
                    socketId = null,
                    success = true
                )
                
                flow.value = Success(optionDetails)
            } catch (e: Exception) {
                // Return empty option details on error
                flow.value = Success(OptionDetails(
                    command = "getOptionSetByItemIdOk",
                    optionSets = emptyList(),
                    success = false
                ))
            }
        }
        
        return flow
    }

    // Extension functions to convert database entities back to API models
    fun OptionSetEntity.toOptionSet(options: List<OptionEntity>): OptionSet {
        return OptionSet(
            id = this.optionSetId?.toInt(),
            name = this.name,
            condition = this.condition?.toInt(),
            customNumber = this.customNumber?.toInt(),
            displayOrder = this.displayOrder?.toInt(),
            itemId = this.itemId?.toInt(),
            status = this.status?.toInt(),
            variantType = this.variantType?.toInt(),
            options = options.map { it.toOption() }
        )
    }

    fun OptionEntity.toOption(): Option {
        return Option(
            id = this.optionId?.toInt(),
            name = this.name,
            price = this.price,
            displayOrder = this.displayOrder?.toInt(),
            status = this.status?.toInt(),
            quantity = this.quantity?.toInt(),
            initialQuantity = this.initialQuantity?.toInt(),
            optionSetId = this.optionSetId?.toInt()
        )
    }
} 