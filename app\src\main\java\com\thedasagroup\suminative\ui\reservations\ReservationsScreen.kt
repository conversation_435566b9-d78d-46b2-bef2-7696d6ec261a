package com.thedasagroup.suminative.ui.reservations

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material3.*
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.compose.mavericksViewModel
import com.google.accompanist.swiperefresh.SwipeRefresh
import com.google.accompanist.swiperefresh.rememberSwipeRefreshState
import com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest
import com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest
import com.thedasagroup.suminative.data.model.response.reservations.Reservation
import com.thedasagroup.suminative.ui.theme.fontPoppins
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.TimeZone
import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.util.Log

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReservationsScreen(
    modifier: Modifier = Modifier,
    viewModel: ReservationsViewModel
) {
    val state by viewModel.collectAsState()

    LaunchedEffect(key1 = "getReservations") {
        viewModel.loadActiveReservations()
        viewModel.loadAllReservations()
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
            .padding(top = 80.dp, start = 16.dp, end = 16.dp, bottom = 16.dp)
    ) {
        // Tab Row
        ReservationsTabRow(
            selectedTabIndex = state.selectedTabIndex,
            onTabSelected = { viewModel.setSelectedTab(it) }
        )

        Spacer(modifier = Modifier.height(16.dp))

        // SwipeRefresh wrapper for content
        SwipeRefresh(
            state = rememberSwipeRefreshState(isRefreshing = state.isRefreshing),
            onRefresh = { viewModel.refresh() },
            modifier = Modifier.fillMaxSize()
        ) {
            // Content based on selected tab
            when (state.selectedTabIndex) {
                0 -> ActiveReservationsContent(
                    reservationsResponse = state.activeReservationsResponse,
                    onEditReservation = { reservation ->
                        viewModel.showEditDialog(reservation)
                    },
                    onCancelReservation = { reservationId ->
                        viewModel.cancelReservation(reservationId)
                    }
                )

                1 -> HistoryReservationsContent(
                    reservationsResponse = state.allReservationsResponse
                )
            }
        }

        // Edit Dialog
        if (state.showEditDialog && state.editingReservation != null) {
            EditReservationDialog(
                editData = state.editingReservation ?: EditReservationData(0, "", "", "", "", 0),
                onDismiss = { viewModel.hideEditDialog() },
                onSave = { request ->
                    if ((request.id ?: -1) > 0) {
                        viewModel.createReservation(request)
                    }
                    viewModel.hideEditDialog()
                },
                createReservationResponse = state.createReservationResponse
            )
        }
    }
}

@Composable
private fun ReservationsTabRow(
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit
) {
    val tabs = listOf("Active Reservations", "History")

    // Category Navigation with Product Tab Styling
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
    ) {
        // Full-width Tabs starting from edge
        TabRow(
            selectedTabIndex = selectedTabIndex,
            containerColor = Color.Transparent,
            indicator = { tabPositions ->
                // No indicator needed since we're using custom tab backgrounds
            },
        ) {
            tabs.forEachIndexed { index, title ->
                val isSelected = index == selectedTabIndex
                val backgroundColor = if (isSelected) Color(0xFF2E7D32) else Color.Transparent

                Tab(
                    selected = isSelected,
                    onClick = {
                        onTabSelected(index)
                    },
                    modifier = Modifier
                        .padding(horizontal = 4.dp, vertical = 8.dp)
                        .background(
                            color = backgroundColor,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .then(
                            if (!isSelected) {
                                Modifier.border(
                                    width = 2.dp,
                                    color = Color(0xFF2E7D32),
                                    shape = RoundedCornerShape(8.dp)
                                )
                            } else Modifier
                        )
                        .clip(RoundedCornerShape(8.dp)),
                    text = {
                        Text(
                            text = title,
                            color = if (isSelected) Color.White else Color(0xFF2E7D32),
                            fontFamily = fontPoppins,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                        )
                    }
                )
            }
        }
    }
}

@Composable
private fun ActiveReservationsContent(
    reservationsResponse: com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse>,
    onEditReservation: (com.thedasagroup.suminative.data.model.response.reservations.Reservation) -> Unit,
    onCancelReservation: (Int) -> Unit
) {
    when (reservationsResponse) {
        is Loading -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color(0xFF2E7D32))
            }
        }

        is Success -> {
            val reservations = reservationsResponse.invoke().reservations

            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Active Reservations",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                ReservationsTable(
                    reservations = reservations,
                    showActions = true,
                    onEditReservation = onEditReservation,
                    onCancelReservation = onCancelReservation
                )
            }
        }

        is Fail -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Failed to load active reservations",
                    color = Color.Red
                )
                Log.v("ReservationsScreen", "Failed to load active reservations: ${reservationsResponse.error.message}")
            }
        }

        else -> {

        }
    }
}

@Composable
private fun HistoryReservationsContent(
    reservationsResponse: com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse>
) {
    when (reservationsResponse) {
        is Loading -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color(0xFF2E7D32))
            }
        }

        is Success -> {
            val reservations = reservationsResponse.invoke().reservations

            Column(modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally) {
                Text(
                    text = "History",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                ReservationsTable(
                    reservations = reservations,
                    showActions = false,
                    onEditReservation = { },
                    onCancelReservation = { }
                )
            }
        }

        else -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Failed to load reservation history",
                    color = Color.Red
                )
            }
        }
    }
}

@Composable
private fun ReservationsTable(
    reservations: List<Reservation>,
    showActions: Boolean,
    onEditReservation: (Reservation) -> Unit,
    onCancelReservation: (Int) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column {
            // Table Header
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color(0xFF2E7D32))
                    .padding(12.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                TableHeaderCell("Customer Name", weight = 1.5f)
                TableHeaderCell("Customer Phone", weight = 1.2f)
                TableHeaderCell("Table Name", weight = 1f)
                TableHeaderCell("When", weight = 1.2f)
                TableHeaderCell("Party", weight = 0.8f)
                TableHeaderCell("Status", weight = 1f)
                if (showActions) {
                    TableHeaderCell("Actions", weight = 1.2f)
                }
            }

            // Table Rows
            LazyColumn {
                items(reservations) { reservation ->
                    ReservationRow(
                        reservation = reservation,
                        showActions = showActions,
                        onEditReservation = onEditReservation,
                        onCancelReservation = onCancelReservation
                    )
                }
            }
        }
    }
}

@Composable
private fun RowScope.TableHeaderCell(
    text: String,
    weight: Float
) {
    Text(
        text = text,
        modifier = Modifier.weight(weight),
        color = Color.White,
        fontWeight = FontWeight.Bold,
        fontSize = 14.sp,
        textAlign = TextAlign.Center
    )
}

@Composable
private fun ReservationRow(
    reservation: Reservation,
    showActions: Boolean,
    onEditReservation: (Reservation) -> Unit,
    onCancelReservation: (Int) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .border(width = 0.5.dp, color = Color.Gray.copy(alpha = 0.3f))
            .padding(12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        TableCell(reservation.customerName ?: "", weight = 1.5f)
        TableCell(reservation.customerPhone ?: "", weight = 1.2f)
        TableCell(reservation.tableName ?: "", weight = 1f)
        TableCell(formatDateTime(reservation.reservationTime ?: ""), weight = 1.2f)
        TableCell(reservation.numPeople.toString(), weight = 0.8f)

        // Status Cell with color
        Text(
            text = getReservationStatusText(reservation.reservationStatus ?: 0),
            modifier = Modifier.weight(1f),
            fontSize = 13.sp,
            textAlign = TextAlign.Center,
            color = getReservationStatusColor(reservation.reservationStatus ?: 0),
            fontWeight = FontWeight.Bold
        )

        if (showActions) {
            Row(
                modifier = Modifier.weight(1.2f),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        onEditReservation(reservation)
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF2E7D32),
                        contentColor = Color.White
                    ),
                    shape = RoundedCornerShape(6.dp),
                    modifier = Modifier.height(32.dp)
                ) {
                    Text("EDIT", fontSize = 12.sp, fontWeight = FontWeight.Bold)
                }

                Button(
                    onClick = {
                        onCancelReservation(reservation.id ?: -1) // Using dummy ID for now
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Red,
                        contentColor = Color.White
                    ),
                    shape = RoundedCornerShape(6.dp),
                    modifier = Modifier.height(32.dp)
                ) {
                    Text("CANCEL", fontSize = 12.sp, fontWeight = FontWeight.Bold)
                }
            }
        }
    }
}

@Composable
private fun RowScope.TableCell(
    text: String,
    weight: Float
) {
    Text(
        text = text,
        modifier = Modifier.weight(weight),
        fontSize = 13.sp,
        textAlign = TextAlign.Center,
        color = Color.Black
    )
}

private fun formatDateTime(dateTimeString: String): String {
    return try {
        val dateTime = LocalDateTime.parse(dateTimeString)
        dateTime.format(DateTimeFormatter.ofPattern("M/d/yy, h:mm a"))
    } catch (e: Exception) {
        dateTimeString // Return original if parsing fails
    }
}

private fun getReservationStatusText(status: Int): String {
    return when (status) {
        0 -> "Cancelled"
        1 -> "Reserved"
        2 -> "Arrived"
        3 -> "DidNotShowUp"
        5 -> "Completed"
        else -> "Reserved"
    }
}

private fun getReservationStatusColor(status: Int): Color {
    return when (status) {
        0 -> Color.Red // Cancelled - Red
        1 -> Color(0xFF2E7D32) // Reserved - Green
        2 -> Color(0xFF2E7D32) // Arrived - Green
        3 -> Color.Red // DidNotShowUp - Red
        4 -> Color(0xFF2E7D32) // Completed - Green
        else -> Color(0xFF2E7D32) // Default - Green
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EditReservationDialog(
    editData: EditReservationData,
    onDismiss: () -> Unit,
    onSave: (CreateReservationRequest) -> Unit,
    createReservationResponse: com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse>
) {
    val context = LocalContext.current

    var customerName by remember { mutableStateOf(editData.customerName) }
    var customerPhone by remember { mutableStateOf(editData.customerPhone) }
    var tableName by remember { mutableStateOf(editData.tableName) }
    var numPeople by remember { mutableStateOf(editData.numPeople.toString()) }
    var reservationDate by remember { mutableStateOf(editData.reservationTime.split("T")[0]) }
    var reservationTime by remember { mutableStateOf(editData.reservationTime.split("T")[1]) }
    var selectedStatus by remember { mutableStateOf(editData.reservationStatus) }
    var showStatusDropdown by remember { mutableStateOf(false) }

    // Parse initial date and time for pickers
    val initialDate = try {
        LocalDate.parse(reservationDate)
    } catch (e: Exception) {
        LocalDate.now()
    }

    val initialTime = try {
        LocalTime.parse(reservationTime)
    } catch (e: Exception) {
        LocalTime.now()
    }

    val reservationStatuses = listOf(
        0 to "Reserved",
        1 to "Cancelled",
        2 to "Arrived",
        3 to "Failed",
        4 to "Completed"
    )

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.8f)
                .fillMaxHeight(0.9f)
                .padding(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Edit Reservation",
                        fontSize = 28.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black
                    )

                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Close",
                            tint = Color.Gray
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Form Fields in two columns for tablet layout
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(24.dp)
                ) {
                    // Left Column
                    Column(
                        modifier = Modifier.weight(1f),
                        verticalArrangement = Arrangement.spacedBy(20.dp)
                    ) {
                        // Customer Name
                        OutlinedTextField(
                            value = customerName,
                            onValueChange = { customerName = it },
                            label = { Text("Customer Name", fontSize = 16.sp) },
                            modifier = Modifier.fillMaxWidth(),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = Color(0xFF2E7D32),
                                focusedLabelColor = Color(0xFF2E7D32)
                            ),
                            textStyle = androidx.compose.ui.text.TextStyle(fontSize = 18.sp),
                            enabled = false
                        )

                        // Customer Phone
                        OutlinedTextField(
                            value = customerPhone,
                            onValueChange = { customerPhone = it },
                            label = { Text("Customer Phone", fontSize = 16.sp) },
                            modifier = Modifier.fillMaxWidth(),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = Color(0xFF2E7D32),
                                focusedLabelColor = Color(0xFF2E7D32)
                            ),
                            textStyle = androidx.compose.ui.text.TextStyle(fontSize = 18.sp),
                            enabled = false
                        )

                        // Table Name
                        OutlinedTextField(
                            value = tableName,
                            onValueChange = { tableName = it },
                            label = { Text("Table Name", fontSize = 16.sp) },
                            modifier = Modifier.fillMaxWidth(),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = Color(0xFF2E7D32),
                                focusedLabelColor = Color(0xFF2E7D32)
                            ),
                            textStyle = androidx.compose.ui.text.TextStyle(fontSize = 18.sp),
                            enabled = false
                        )
                    }

                    // Right Column
                    Column(
                        modifier = Modifier.weight(1f),
                        verticalArrangement = Arrangement.spacedBy(20.dp)
                    ) {
                        // Number of People
                        OutlinedTextField(
                            value = numPeople,
                            onValueChange = { numPeople = it },
                            label = { Text("Number of People", fontSize = 16.sp) },
                            modifier = Modifier.fillMaxWidth(),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = Color(0xFF2E7D32),
                                focusedLabelColor = Color(0xFF2E7D32)
                            ),
                            textStyle = androidx.compose.ui.text.TextStyle(fontSize = 18.sp),
                            enabled = false
                        )

                        // Reservation Date with Date Picker
                        OutlinedTextField(
                            value = reservationDate,
                            onValueChange = { },
                            label = { Text("Reservation Date", fontSize = 16.sp) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    val currentDate = try {
                                        LocalDate.parse(reservationDate)
                                    } catch (e: Exception) {
                                        LocalDate.now()
                                    }

                                    val datePickerDialog = DatePickerDialog(
                                        context,
                                        { _, year, month, dayOfMonth ->
                                            reservationDate = String.format(
                                                "%04d-%02d-%02d",
                                                year,
                                                month + 1,
                                                dayOfMonth
                                            )
                                        },
                                        currentDate.year,
                                        currentDate.monthValue - 1,
                                        currentDate.dayOfMonth
                                    )
                                    datePickerDialog.show()
                                },
                            enabled = false,
                            colors = OutlinedTextFieldDefaults.colors(
                                disabledBorderColor = Color(0xFF2E7D32),
                                disabledLabelColor = Color(0xFF2E7D32),
                                disabledTextColor = Color.Black
                            ),
                            textStyle = androidx.compose.ui.text.TextStyle(fontSize = 18.sp),
                            trailingIcon = {
                                Icon(
                                    imageVector = Icons.Default.DateRange,
                                    contentDescription = "Select Date",
                                    tint = Color(0xFF2E7D32)
                                )
                            }
                        )

                        // Reservation Time with Time Picker
                        OutlinedTextField(
                            value = reservationTime,
                            onValueChange = { },
                            label = { Text("Reservation Time", fontSize = 16.sp) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    val currentTime = try {
                                        LocalTime.parse(reservationTime)
                                    } catch (e: Exception) {
                                        LocalTime.now()
                                    }

                                    val timePickerDialog = TimePickerDialog(
                                        context,
                                        { _, hourOfDay, minute ->
                                            reservationTime =
                                                String.format("%02d:%02d", hourOfDay, minute)
                                        },
                                        currentTime.hour,
                                        currentTime.minute,
                                        true // 24-hour format
                                    )
                                    timePickerDialog.show()
                                },
                            enabled = false,
                            colors = OutlinedTextFieldDefaults.colors(
                                disabledBorderColor = Color(0xFF2E7D32),
                                disabledLabelColor = Color(0xFF2E7D32),
                                disabledTextColor = Color.Black
                            ),
                            textStyle = androidx.compose.ui.text.TextStyle(fontSize = 18.sp),
                            trailingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Schedule,
                                    contentDescription = "Select Time",
                                    tint = Color(0xFF2E7D32)
                                )
                            }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(20.dp))

                // Status Dropdown
                Box(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    OutlinedTextField(
                        value = reservationStatuses.find { it.first == selectedStatus }?.second
                            ?: "Reserved",
                        onValueChange = { },
                        label = { Text("Status", fontSize = 16.sp) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { showStatusDropdown = true },
                        enabled = false,
                        colors = OutlinedTextFieldDefaults.colors(
                            disabledBorderColor = Color(0xFF2E7D32),
                            disabledLabelColor = Color(0xFF2E7D32),
                            disabledTextColor = Color.Black
                        ),
                        textStyle = androidx.compose.ui.text.TextStyle(fontSize = 18.sp),
                        trailingIcon = {
                            Icon(
                                imageVector = Icons.Default.ArrowDropDown,
                                contentDescription = "Dropdown",
                                tint = Color(0xFF2E7D32)
                            )
                        }
                    )

                    DropdownMenu(
                        expanded = showStatusDropdown,
                        onDismissRequest = { showStatusDropdown = false },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        reservationStatuses.forEach { (statusCode, statusName) ->
                            DropdownMenuItem(
                                text = {
                                    Text(
                                        text = statusName,
                                        fontSize = 18.sp,
                                        color = when (statusCode) {
                                            0 -> Color(0xFF2E7D32) // Reserved - Green
                                            1 -> Color.Red // Cancelled - Red
                                            2 -> Color(0xFF2E7D32) // Arrived - Green
                                            3 -> Color.Red // Failed - Red
                                            4 -> Color(0xFF2E7D32) // Completed - Green
                                            else -> Color.Black
                                        }
                                    )
                                },
                                onClick = {
                                    selectedStatus = statusCode
                                    showStatusDropdown = false
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))

                // Action Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp, Alignment.End)
                ) {
                    Button(
                        onClick = onDismiss,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Gray,
                            contentColor = Color.White
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier
                            .height(56.dp)
                            .width(120.dp)
                    ) {
                        Text(
                            text = "CANCEL",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }

                    Button(
                        onClick = {
                            val request = CreateReservationRequest(
                                id = editData.reservationId,
                                storeId = editData.storeId,
                                tableId = editData.tableId,
                                customerId = editData.customerId,
                                guestName = customerName,
                                guestPhone = customerPhone,
                                numPeople = numPeople.toIntOrNull() ?: editData.numPeople,
                                reservationStatus = selectedStatus,
                                reservationTime = "${reservationDate}T${reservationTime}",
                                timezoneOffset = 0 // Will be automatically calculated by the use case
                            )
                            onSave(request)
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32),
                            contentColor = Color.White
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier
                            .height(56.dp)
                            .width(120.dp),
                        enabled = createReservationResponse !is Loading
                    ) {
                        if (createReservationResponse is Loading) {
                            CircularProgressIndicator(
                                color = Color.White,
                                modifier = Modifier.size(20.dp)
                            )
                        } else {
                            Text(
                                text = "SAVE",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }
            }
        }
    }
}
