CREATE TABLE OrderItemEntity (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    orderId TEXT NOT NULL,
    itemId INTEGER NOT NULL,
    itemName TEXT NOT NULL,
    itemDescription TEXT,
    quantity INTEGER NOT NULL DEFAULT 1,
    unitPrice REAL NOT NULL,
    totalPrice REAL NOT NULL,
    category TEXT,
    modifiers TEXT, -- JSON string for any item modifications
    notes TEXT,
    createdAt INTEGER NOT NULL,
    FOREIGN KEY (orderId) REFERENCES OrderEntity(orderId) ON DELETE CASCADE
);

CREATE INDEX idx_orderitem_order ON OrderItemEntity(orderId);
CREATE INDEX idx_orderitem_item ON OrderItemEntity(itemId);

-- Queries for OrderItem operations
insertOrderItem:
INSERT INTO OrderItemEntity (
    orderId, itemId, itemName, itemDescription, quantity, 
    unitPrice, totalPrice, category, modifiers, notes, createdAt
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

getOrderItemsByOrderId:
SELECT * FROM OrderItemEntity WHERE orderId = ? ORDER BY id ASC;

getOrderItemById:
SELECT * FROM OrderItemEntity WHERE id = ?;

updateOrderItemQuantity:
UPDATE OrderItemEntity 
SET quantity = ?, totalPrice = ?, modifiers = ?
WHERE id = ?;

deleteOrderItem:
DELETE FROM OrderItemEntity WHERE id = ?;

deleteOrderItemsByOrderId:
DELETE FROM OrderItemEntity WHERE orderId = ?;

getOrderItemCount:
SELECT COUNT(*) FROM OrderItemEntity WHERE orderId = ?;

getOrderItemTotal:
SELECT SUM(totalPrice) FROM OrderItemEntity WHERE orderId = ?; 