//package com.thedasagroup.suminative.ui
//
//import com.airbnb.mvrx.Fail
//import com.airbnb.mvrx.Loading
//import com.airbnb.mvrx.Success
//import com.airbnb.mvrx.Uninitialized
//import com.stripe.stripeterminal.external.callable.ConnectionTokenCallback
//import com.stripe.stripeterminal.external.callable.ConnectionTokenProvider
//import com.stripe.stripeterminal.external.models.ConnectionTokenException
//import com.thedasagroup.suminative.data.model.request.payment.GetPaymentSecretRequest
//import com.thedasagroup.suminative.data.model.request.payment.SecretResponse
//import com.thedasagroup.suminative.data.repo.StockRepository
//import kotlinx.coroutines.CoroutineScope
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.launch
//import kotlinx.coroutines.selects.whileSelect
//import kotlinx.serialization.json.Json
//
//class CustomConnectionTokenProvider(
//    private val stockRepository: StockRepository, private val coroutineScope: CoroutineScope
//) : ConnectionTokenProvider {
//    override fun fetchConnectionToken(callback: ConnectionTokenCallback) {
//        try {
//            // Your backend should call /v1/terminal/connection_tokens and return the
//            // JSON response from Stripe. When the request to your backend succeeds,
//            // return the `secret` from the response to the SDK.
//            coroutineScope.launch(Dispatchers.IO) {
//                val response =
//                    stockRepository.getPaymentSecret(request = GetPaymentSecretRequest()).value
//                when (response) {
//                    is Success -> {
//                        val sec = Json.decodeFromString<SecretResponse>(
//                            response().paymentResponseJson ?: ""
//                        )
//                        callback.onSuccess(sec.secret ?: "")
//                    }
//
//                    else -> {
//
//                    }
//                }
//            }
//        } catch (e: Exception) {
//            callback.onFailure(
//                ConnectionTokenException("Failed to fetch connection token", e)
//            )
//        }
//    }
//}