package com.thedasagroup.suminative.ui.orders

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.change_status.ChangeStatusRequest
import com.thedasagroup.suminative.data.model.request.login.BankDetail
import com.thedasagroup.suminative.data.model.request.login.Brand
import com.thedasagroup.suminative.data.model.request.login.Business
import com.thedasagroup.suminative.data.model.request.login.Category
import com.thedasagroup.suminative.data.model.request.login.ChangePassword
import com.thedasagroup.suminative.data.model.request.login.Cms
import com.thedasagroup.suminative.data.model.request.login.Conversation
import com.thedasagroup.suminative.data.model.request.login.Customer
import com.thedasagroup.suminative.data.model.request.login.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.login.DeliverySettingRange
import com.thedasagroup.suminative.data.model.request.login.DeliverySettings
import com.thedasagroup.suminative.data.model.request.login.Extra
import com.thedasagroup.suminative.data.model.request.login.ExtraItemRelation
import com.thedasagroup.suminative.data.model.request.login.FeedbackComplain
import com.thedasagroup.suminative.data.model.request.login.Option
import com.thedasagroup.suminative.data.model.request.login.OptionSet
import com.thedasagroup.suminative.data.model.request.login.Order
import com.thedasagroup.suminative.data.model.request.login.OrderStatus
import com.thedasagroup.suminative.data.model.request.login.PaymentData
import com.thedasagroup.suminative.data.model.request.login.PromoCodes
import com.thedasagroup.suminative.data.model.request.login.Store
import com.thedasagroup.suminative.data.model.request.login.StoreItem
import com.thedasagroup.suminative.data.model.request.login.StoreSetting
import com.thedasagroup.suminative.data.model.request.login.SupportDetail
import com.thedasagroup.suminative.data.model.request.login.UiSettings
import com.thedasagroup.suminative.data.model.request.login.User
import com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse
import com.thedasagroup.suminative.data.model.response.store_orders.OrdersResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.OrdersRepository
import kotlinx.coroutines.flow.StateFlow

class ChangeStatusUseCase(
    private val repo: OrdersRepository, private val prefs: Prefs
) {
    suspend operator fun invoke(
        orderId: Int, status: Int
    ): StateFlow<Async<ChangeStatusResponse>> {

        return repo.changeStatus(
            request = ChangeStatusRequest(
                orderStatus = OrderStatus(
                    id = -1,
                    note = "",
                    orderId = orderId,
                    status = status,
                    updatedBy = prefs.loginResponse?.user?.id ?: -1,
                    updatedOn = ""
                ),
                command = "changeStatusOfAnOrder"
            )
        )
    }
}