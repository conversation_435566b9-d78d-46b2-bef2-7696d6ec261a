package com.thedasagroup.suminative.data.model.response.my_guava.orders.updateorder

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class UpdateOrderRequest(
    @SerialName("additionalAmount")
    val additionalAmount: String? = null,
    @SerialName("cashbackAmount")
    val cashbackAmount: String? = null,
    @SerialName("ccy")
    val ccy: String? = null,
    @SerialName("clientReference")
    val clientReference: String? = null,
    @SerialName("description")
    val description: String? = null,
    @SerialName("expireAt")
    val expireAt: String? = null,
    @SerialName("paymentMethod")
    val paymentMethod: String? = null,
    @SerialName("serviceChargeAmount")
    val serviceChargeAmount: String? = null,
    @SerialName("taxAmount")
    val taxAmount: String? = null,
    @SerialName("taxId")
    val taxId: String? = null,
    @SerialName("taxName")
    val taxName: String? = null,
    @SerialName("tipsAmount")
    val tipsAmount: String? = null,
    @SerialName("totalAmount")
    val totalAmount: String? = null,
    @SerialName("transactionAmount")
    val transactionAmount: String? = null,
    @SerialName("transactionType")
    val transactionType: String? = null,
    @SerialName("userAgent")
    val userAgent: String? = null,
    @SerialName("voidReason")
    val voidReason: String? = null
)