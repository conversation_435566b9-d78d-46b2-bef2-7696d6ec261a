package com.thedasagroup.suminative.ui.stock

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.BuildConfig
import com.thedasagroup.suminative.data.database.CategoryRepository
import com.thedasagroup.suminative.data.model.request.category_sorting.CategorySortingRequest
import com.thedasagroup.suminative.data.model.request.stock.GetPagedStockItemsRequest
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.ProductRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.domain.categories.SyncCategoriesUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

open class StockUseCase(
    private val stockRepository: StockRepository, 
    private val prefs: Prefs,
    private val syncCategoriesUseCase: SyncCategoriesUseCase
) {
    suspend operator fun invoke(): StateFlow<Async<StockItemsResponse>> {

        val stockItems = stockRepository.getPagedStockItems(
            request = GetPagedStockItemsRequest(storeId = prefs.store?.id ?: 0)
        )

        when (stockItems.value) {
            is Success -> {
                val filteredItems = if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
                    stockItems.value()?.items?.filter {
                        it.brandId == prefs.loginResponse?.brandId
                    }
                } else {
                    stockItems.value()?.items
                }

                val storeId = prefs.store?.id ?: 0L
                
                // Try to get categories from database first
                val sortedCategories = getCategoriesFromApiDirectly(storeId.toLong())

                val sortedItems = if (sortedCategories.isNotEmpty()) {
                    // Sort the items based on their category's position in the sorted list
                    filteredItems?.sortedWith { item1, item2 ->
                        val index1 = sortedCategories.indexOf(item1.category)
                        val index2 = sortedCategories.indexOf(item2.category)

                        when {
                            index1 == -1 && index2 == -1 -> 0 // Both categories not in the sort list
                            index1 == -1 -> 1 // First category not in sort list, put it after
                            index2 == -1 -> -1 // Second category not in sort list, put it after
                            else -> index1.compareTo(index2) // Both in sort list, compare positions
                        }
                    }
                } else {
                    filteredItems // If no sorting data available, use unsorted list
                }

                return MutableStateFlow(
                    Success(
                        stockItems.value()?.copy(items = sortedItems) ?: StockItemsResponse()
                    )
                )
            }

            else -> {
                return stockItems
            }
        }
    }
    
    // Get categories for sorting with database-first approach
//    private suspend fun getCategoriesForSorting(storeId: Long): List<String> {
//        return try {
//            // Check if categories exist in database
//            if (syncCategoriesUseCase.hasCategoriesInDatabase(storeId)) {
//                // Get categories from database
//                syncCategoriesUseCase.getCategoriesFromDatabase(storeId)
//            } else {
//                // Fallback to API and sync to database
//                val syncResult = syncCategoriesUseCase.invoke(forceSync = false)
//                if (syncResult.isSuccess) {
//                    syncCategoriesUseCase.getCategoriesFromDatabase(storeId)
//                } else {
//                    // If sync fails, try to get from API directly as final fallback
//                    getCategoriesFromApiDirectly(storeId)
//                }
//            }
//        } catch (e: Exception) {
//            // Final fallback to API
//            getCategoriesFromApiDirectly(storeId)
//        }
//    }
    
    // Fallback method to get categories directly from API (original approach)
    private suspend fun getCategoriesFromApiDirectly(storeId: Long): List<String> {
        return try {
            val categorySortingResult = stockRepository.getCategorySorting(
                CategorySortingRequest(storeId = storeId.toString())
            )
            
            if (categorySortingResult.value is Success) {
                categorySortingResult.value()?.categories ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
}