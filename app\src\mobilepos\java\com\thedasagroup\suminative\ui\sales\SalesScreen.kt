package com.thedasagroup.suminative.ui.sales

import android.graphics.Bitmap
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Card
import androidx.compose.material.LocalTextStyle
import androidx.compose.material.MaterialTheme
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Assessment
import androidx.compose.material.icons.filled.CalendarToday
import androidx.compose.material.icons.filled.CreditCard
import androidx.compose.material.icons.filled.MonetizationOn
import androidx.compose.material.icons.filled.Print
import androidx.compose.material.icons.filled.Receipt
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.navigation.compose.rememberNavController
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem2
import com.thedasagroup.suminative.data.model.request.sales.SalesRequest
import com.thedasagroup.suminative.data.model.request.stock.ChangeStockRequest
import com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse
import com.thedasagroup.suminative.data.model.response.sales.SalesResponse
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.ui.common.customComposableViews.CustomDateRangePicker
import com.thedasagroup.suminative.ui.common.customComposableViews.DateRange
import com.thedasagroup.suminative.ui.common.customComposableViews.DateRangeDropdown
import com.thedasagroup.suminative.ui.orders.screenshotableComposable
import com.thedasagroup.suminative.ui.print.DateComposable
import com.thedasagroup.suminative.ui.print.MyTextDivider
import com.thedasagroup.suminative.ui.print.PrintingBill2
import com.thedasagroup.suminative.ui.print.Total
import com.thedasagroup.suminative.ui.print.verticalScrollbar
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.stock.ChangeStockDialog
import com.thedasagroup.suminative.ui.stock.StockScreenState
import com.thedasagroup.suminative.ui.stock.tabRow
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_APP
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END2
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_DATE_ONLY
import com.thedasagroup.suminative.ui.utils.formatDate
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.text.isNotEmpty

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SalesScreenMain(
    onBackClick: () -> Unit, 
    viewModel: ProductsScreenViewModel,
    onPrintBill: (Bitmap) -> Unit
) {
    LaunchedEffect(key1 = "getSales") {
        withContext(Dispatchers.IO) {
            val salesRequest = SalesRequest(
                storeId = viewModel.prefs.store?.id ?: 0,
                startDate = viewModel.trueTimeImpl.now().formatDate(DATE_FORMAT_DATE_ONLY),
                endDate = viewModel.trueTimeImpl.now().formatDate(DATE_FORMAT_DATE_ONLY),
            )

            viewModel.getTotalSales(request = salesRequest)
            viewModel.getSalesReport(request = salesRequest)
        }
    }

    val showSaleReportDialog by viewModel.collectAsState(ProductsScreenState::showSalesReportDialog)
    val coroutineScope = rememberCoroutineScope()

    if (showSaleReportDialog) {
        SalesReportDialog(
            modifier = Modifier,
            onPrintBill = { onPrintBill(it) },
            onCancel = { viewModel.showSalesReportDialog(show = false) },
            productsScreenViewModel = viewModel
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
            .statusBarsPadding()
    ) {
        // Mobile Header
        MobileSalesHeader(onBackClick = onBackClick)
        
        // Sales Content
        SalesScreen(
            viewModel = viewModel,
            onSalesReportClick = { viewModel.showSalesReportDialog(show = true) },
            updateSalesReport = { startDate, endDate ->
                coroutineScope.launch(Dispatchers.IO) {
                    viewModel.getTotalSales(
                        request = SalesRequest(
                            storeId = viewModel.prefs.store?.id ?: 0,
                            startDate = startDate,
                            endDate = endDate,
                        )
                    )
                    viewModel.getSalesReport(
                        request = SalesRequest(
                            storeId = viewModel.prefs.store?.id ?: 0,
                            startDate = startDate,
                            endDate = endDate,
                        )
                    )
                }
            }
        )
    }
}

@Composable
private fun MobileSalesHeader(onBackClick: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            onClick = onBackClick,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "Back",
                tint = Color(0xFF2E7D32),
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Text(
            text = "Sales Report",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF2E7D32),
            fontFamily = fontPoppins
        )
    }
}

@Composable
fun SalesScreen(
    viewModel: ProductsScreenViewModel,
    onSalesReportClick: () -> Unit,
    updateSalesReport: (String, String) -> Unit
) {
    val salesResponse by viewModel.collectAsState(ProductsScreenState::salesResponse)
    val salesRequest by viewModel.collectAsState(ProductsScreenState::salesRequest)
    
    var selectedDateRange by remember { mutableStateOf(DateRange.Today) }
    var customStartDate by remember { mutableStateOf(salesRequest?.startDate ?: "") }
    var customEndDate by remember { mutableStateOf(salesRequest?.endDate ?: "") }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Date Range Selection Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = 4.dp,
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.CalendarToday,
                        contentDescription = null,
                        tint = Color(0xFF2E7D32),
                        modifier = Modifier.size(20.dp)
                    )
                    Text(
                        text = "Select Date Range",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black,
                        fontFamily = fontPoppins
                    )
                }
                
                DateRangeDropdown(
                    selectedRange = selectedDateRange,
                    onRangeSelected = { startDate, endDate, range ->
                        selectedDateRange = range
                        customStartDate = startDate
                        customEndDate = endDate
                        updateSalesReport(startDate, endDate)
                    },
                    viewModel = viewModel
                )
                
                if (customStartDate.isNotEmpty() && customEndDate.isNotEmpty()) {
                    val rangeText = if (customStartDate == customEndDate) {
                        customStartDate
                    } else {
                        "$customStartDate to $customEndDate"
                    }
                    
                    Text(
                        text = "Viewing: $rangeText",
                        fontSize = 14.sp,
                        fontStyle = FontStyle.Italic,
                        color = Color(0xFF2E7D32),
                        fontFamily = fontPoppins
                    )
                }
                
                Button(
                    onClick = onSalesReportClick,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF2E7D32),
                        contentColor = Color.White
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Print,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "View Detailed Report",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            }
        }
        
        // Sales Summary Cards
        when (salesResponse) {
            is Loading -> {
                LoadingSalesCards()
            }
            is Success -> {
                val data = salesResponse()
                SalesDataCards(
                    totalSales = data?.numberOfSales?.toString() ?: "0",
                    totalAmount = data?.totalAmount?.transformDecimal() ?: "0.00"
                )
            }
            else -> {
                SalesDataCards(totalSales = "0", totalAmount = "0.00")
            }
        }
    }
}

@Composable
private fun LoadingSalesCards() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        repeat(2) {
            Card(
                modifier = Modifier.weight(1f),
                elevation = 4.dp,
                shape = RoundedCornerShape(12.dp)
            ) {
                Column(
                    modifier = Modifier.padding(20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = Color(0xFF2E7D32)
                    )
                    Text(
                        text = "Loading...",
                        fontSize = 14.sp,
                        color = Color.Black.copy(alpha = 0.7f),
                        fontFamily = fontPoppins
                    )
                }
            }
        }
    }
}

@Composable
private fun SalesDataCards(totalSales: String, totalAmount: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // Total Sales Card
        SalesCard(
            modifier = Modifier.weight(1f),
            icon = Icons.Default.ShoppingCart,
            title = "Total Sales",
            value = totalSales,
            subtitle = "Orders"
        )
        
        // Total Amount Card
        SalesCard(
            modifier = Modifier.weight(1f),
            icon = Icons.Default.MonetizationOn,
            title = "Total Amount",
            value = "£$totalAmount",
            subtitle = "Revenue"
        )
    }
}

@Composable
private fun SalesCard(
    modifier: Modifier = Modifier,
    icon: ImageVector,
    title: String,
    value: String,
    subtitle: String
) {
    Card(
        modifier = modifier,
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        Color(0xFF2E7D32).copy(alpha = 0.1f),
                        RoundedCornerShape(24.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color(0xFF2E7D32),
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Text(
                text = title,
                fontSize = 14.sp,
                color = Color.Black.copy(alpha = 0.7f),
                fontFamily = fontPoppins,
                textAlign = TextAlign.Center
            )
            
            Text(
                text = value,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2E7D32),
                fontFamily = fontPoppins,
                textAlign = TextAlign.Center
            )
            
            Text(
                text = subtitle,
                fontSize = 12.sp,
                color = Color.Black.copy(alpha = 0.5f),
                fontFamily = fontPoppins,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun SalesReportDialog(
    modifier: Modifier = Modifier,
    onPrintBill: (Bitmap) -> Unit,
    onCancel: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val salesReportResponse by productsScreenViewModel.collectAsState(ProductsScreenState::salesReportResponse)
    val shouldPrintInstant by productsScreenViewModel.collectAsState(ProductsScreenState::shouldPrintInstant)
    val salesRequest by productsScreenViewModel.collectAsState(ProductsScreenState::salesRequest)
    
    val startDate = salesRequest?.startDate ?: ""
    val endDate = salesRequest?.endDate ?: ""
    val dateRangeText = if (startDate.isNotEmpty() && endDate.isNotEmpty()) {
        if (startDate == endDate) startDate else "$startDate to $endDate"
    } else ""

    val screenshot = screenshotableComposable(content = {
        Column(
            modifier = Modifier
                .width(400.dp)
                .background(color = Color.White)
                .padding(8.dp)
        ) {
            SalesReportPrintingBill(
                salesReportResponse = salesReportResponse() ?: SalesReportResponse(),
                productsScreenViewModel = productsScreenViewModel,
                prefs = productsScreenViewModel.prefs,
                dateRangeText = dateRangeText
            )
        }
    })

    Dialog(onDismissRequest = { onCancel() }) {
        Card(
            shape = RoundedCornerShape(16.dp),
            modifier = Modifier.padding(16.dp),
        ) {
            when (salesReportResponse) {
                is Loading -> {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(color = Color(0xFF2E7D32))
                    }
                }
                else -> {
                    Column(
                        modifier = Modifier.background(Color.White)
                    ) {
                        // Header
                        if (dateRangeText.isNotEmpty()) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(Color(0xFF2E7D32))
                                    .padding(16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "Sales Report: $dateRangeText",
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = Color.White,
                                    fontFamily = fontPoppins
                                )
                            }
                        }
                        
                        if (shouldPrintInstant) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(32.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator(color = Color(0xFF2E7D32))
                            }
                            val bitmap = screenshot.invoke()
                            onPrintBill(bitmap)
                        }
                        
                        screenshot()
                        
                        // Content
                        Column(
                            modifier = Modifier
                                .width(400.dp)
                                .background(color = Color.White)
                                .padding(16.dp)
                                .height(400.dp)
                                .verticalScroll(rememberScrollState())
                        ) {
                            SalesReportPrintingBill(
                                salesReportResponse = salesReportResponse() ?: SalesReportResponse(),
                                productsScreenViewModel = productsScreenViewModel,
                                prefs = productsScreenViewModel.prefs,
                                dateRangeText = dateRangeText
                            )
                        }
                        
                        // Action buttons
                        if (!shouldPrintInstant) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(Color(0xFFF5F5F5))
                                    .padding(16.dp),
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                TextButton(
                                    onClick = onCancel,
                                    modifier = Modifier.weight(1f),
                                    colors = ButtonDefaults.textButtonColors(
                                        contentColor = Color(0xFF2E7D32)
                                    )
                                ) {
                                    Text(
                                        "Close",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium,
                                        fontFamily = fontPoppins
                                    )
                                }
                                
                                /*Button(
                                    onClick = {
                                        val bitmap = screenshot.invoke()
                                        onPrintBill(bitmap)
                                    },
                                    modifier = Modifier.weight(1f),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Color(0xFF2E7D32),
                                        contentColor = Color.White
                                    ),
                                    shape = RoundedCornerShape(8.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Print,
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        "Print",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold,
                                        fontFamily = fontPoppins
                                    )
                                }*/
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun SalesReportPrintingBill(
    salesReportResponse: SalesReportResponse, 
    prefs: Prefs,
    productsScreenViewModel: ProductsScreenViewModel,
    dateRangeText: String
) {
    val order = salesReportResponse
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header Section
        MobileSalesReportHeader(
            businessName = prefs.loginResponse?.businesses?.name ?: "",
            dateRange = dateRangeText
        )
        
        // Sales Summary Section
        MobileSalesSection(order = order)
        
        // Payment Breakdown Section
        MobilePaymentSection(order = order)
        
        // Total Transactions Summary
        MobileTotalTransactionsCard(order = order)
    }
}

@Composable
private fun MobileSalesReportHeader(
    businessName: String,
    dateRange: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 2.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .background(Color(0xFF2E7D32))
                .padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "Sales Report",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                fontFamily = fontPoppins,
                textAlign = TextAlign.Center
            )
            
            if (businessName.isNotEmpty()) {
                Text(
                    text = businessName,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White,
                    fontFamily = fontPoppins,
                    textAlign = TextAlign.Center
                )
            }
            
            if (dateRange.isNotEmpty()) {
                Text(
                    text = "Period: $dateRange",
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.9f),
                    fontFamily = fontPoppins,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun MobileSalesSection(order: SalesReportResponse) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 2.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .background(Color.White)
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Section Header
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Assessment,
                    contentDescription = null,
                    tint = Color(0xFF2E7D32),
                    modifier = Modifier.size(20.dp)
                )
                Text(
                    text = "Sales Breakdown",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            }
            
            // Sales Items
            MobileSalesItem(
                title = "Total Item Sales",
                value = "£${(order.itemSales)?.transformDecimal() ?: "0.00"}"
            )
            
            val totalVatItems = order.vatItemsTotal ?: 0.0
            MobileSalesItem(
                title = "Total VAT Items",
                value = "£${totalVatItems.transformDecimal()}"
            )
            
            MobileSalesItem(
                title = "VAT",
                value = "£${(order.vat ?: 0.0).transformDecimal()}"
            )
            
            val nonVatItems = if ((order.nonVatItemsTotal ?: 0.0) >= 0) {
                order.nonVatItemsTotal ?: 0.0
            } else {
                0.0
            }
            MobileSalesItem(
                title = "Non VAT Items",
                value = "£${nonVatItems.transformDecimal()}"
            )
            
            MobileSalesItem(
                title = "Delivery Charge",
                value = "£${(order.deliveryCharge ?: 0.0).transformDecimal()}"
            )
            
            Divider(
                color = Color(0xFF2E7D32).copy(alpha = 0.3f),
                thickness = 1.dp,
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            // Net Sales (highlighted)
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        Color(0xFF2E7D32).copy(alpha = 0.1f),
                        RoundedCornerShape(8.dp)
                    )
                    .padding(12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Net Sales",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
                Text(
                    text = "£${(order.netSales ?: 0.0).transformDecimal()}",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            }
        }
    }
}

@Composable
private fun MobilePaymentSection(order: SalesReportResponse) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 2.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .background(Color.White)
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Section Header
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.CreditCard,
                    contentDescription = null,
                    tint = Color(0xFF2E7D32),
                    modifier = Modifier.size(20.dp)
                )
                Text(
                    text = "Payment Breakdown",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            }
            
            // Payment Methods in Cards
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // Card Payments
                MobilePaymentCard(
                    modifier = Modifier.weight(1f),
                    icon = Icons.Default.CreditCard,
                    title = "Card",
                    count = order.cardOrders ?: 0,
                    amount = order.cardPayment ?: 0.0
                )
                
                // Cash Payments
                MobilePaymentCard(
                    modifier = Modifier.weight(1f),
                    icon = Icons.Default.MonetizationOn,
                    title = "Cash",
                    count = order.cashOrders ?: 0,
                    amount = order.cashPayment ?: 0.0
                )
            }
        }
    }
}

@Composable
private fun MobilePaymentCard(
    modifier: Modifier = Modifier,
    icon: ImageVector,
    title: String,
    count: Int,
    amount: Double
) {
    Card(
        modifier = modifier,
        elevation = 1.dp,
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .background(Color(0xFFF5F5F5))
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = Color(0xFF2E7D32),
                modifier = Modifier.size(24.dp)
            )
            
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black,
                fontFamily = fontPoppins
            )
            
            Text(
                text = "$count orders",
                fontSize = 12.sp,
                color = Color.Black.copy(alpha = 0.7f),
                fontFamily = fontPoppins
            )
            
            Text(
                text = "£${amount.transformDecimal()}",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2E7D32),
                fontFamily = fontPoppins
            )
        }
    }
}

@Composable
private fun MobileTotalTransactionsCard(order: SalesReportResponse) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 2.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .background(Color(0xFF2E7D32))
                .padding(20.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Total Transactions",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White,
                    fontFamily = fontPoppins
                )
                Text(
                    text = "All payment methods",
                    fontSize = 12.sp,
                    color = Color.White.copy(alpha = 0.8f),
                    fontFamily = fontPoppins
                )
            }
            
            Text(
                text = "${((order.cardOrders ?: 0) + (order.cashOrders ?: 0))}",
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                fontFamily = fontPoppins
            )
        }
    }
}

@Composable
private fun MobileSalesItem(
    title: String,
    value: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            fontSize = 14.sp,
            color = Color.Black,
            fontFamily = fontPoppins,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Black,
            fontFamily = fontPoppins
        )
    }
}

@Composable
fun AutoResizeText(
    text: String,
    fontSizeRange: FontSizeRange,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    style: TextStyle = LocalTextStyle.current,
) {
    var fontSizeValue by remember { mutableStateOf(fontSizeRange.max.value) }
    var readyToDraw by remember { mutableStateOf(false) }

    Text(
        text = text,
        color = color,
        maxLines = maxLines,
        fontStyle = fontStyle,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        letterSpacing = letterSpacing,
        textDecoration = textDecoration,
        textAlign = textAlign,
        lineHeight = lineHeight,
        overflow = overflow,
        softWrap = softWrap,
        style = style,
        fontSize = fontSizeValue.sp,
        onTextLayout = {
            if (it.didOverflowHeight && !readyToDraw) {
                val nextFontSizeValue = fontSizeValue - fontSizeRange.step.value
                if (nextFontSizeValue <= fontSizeRange.min.value) {
                    // Reached minimum, set minimum font size and it's readToDraw
                    fontSizeValue = fontSizeRange.min.value
                    readyToDraw = true
                } else {
                    // Text doesn't fit yet and haven't reached minimum text range, keep decreasing
                    fontSizeValue = nextFontSizeValue
                }
            } else {
                // Text fits before reaching the minimum, it's readyToDraw
                readyToDraw = true
            }
        },
        modifier = modifier.drawWithContent { if (readyToDraw) drawContent() }
    )
}

data class FontSizeRange(
    val min: TextUnit,
    val max: TextUnit,
    val step: TextUnit = DEFAULT_TEXT_STEP,
) {
    init {
        require(min < max) { "min should be less than max, $this" }
        require(step.value > 0) { "step should be greater than 0, $this" }
    }

    companion object {
        private val DEFAULT_TEXT_STEP = 1.sp
    }
}
