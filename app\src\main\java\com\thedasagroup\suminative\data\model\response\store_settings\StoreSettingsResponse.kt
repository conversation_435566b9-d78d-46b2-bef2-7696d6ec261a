package com.thedasagroup.suminative.data.model.response.store_settings

import com.thedasagroup.suminative.data.model.response.login.Businesse
import com.thedasagroup.suminative.data.model.response.login.Country
import com.thedasagroup.suminative.data.model.response.login.MyStoreSettings
import com.thedasagroup.suminative.data.model.response.login.User
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class StoreSettingsResponse(
    @SerialName("businesses")
    val businesses: List<Businesse>? = mutableListOf(),
    @SerialName("command")
    val command: String? = null,
    @SerialName("countries")
    val countries: List<Country>? = mutableListOf(),
    @SerialName("myStoreSettings")
    val myStoreSettings: MyStoreSettings? = null,
    @SerialName("paymentId")
    val paymentId: Int? = null,
    @SerialName("paymentResponseJson")
    val paymentResponseJson: String? = null,
    @SerialName("socketId")
    val socketId: String? = null,
    @SerialName("success")
    val success: Boolean? = false,
    @SerialName("user")
    val user: User? = null,
    @SerialName("store")
    val store: Store2? = null,
)