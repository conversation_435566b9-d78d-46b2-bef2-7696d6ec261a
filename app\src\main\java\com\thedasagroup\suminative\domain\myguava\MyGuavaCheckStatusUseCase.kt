package com.thedasagroup.suminative.domain.myguava

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Success
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.my_guava.orders.CreateOrderRequest
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse
import com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session
import com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.formatDate
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.UUID

open class MyGuavaCheckStatusUseCase(
        private val guavaRepository: MyGuavaRepository
) {
    suspend operator fun invoke(sessionId : String): StateFlow<Async<Session>> {
        val sessionResponse = guavaRepository.getSessionById(sessionId = sessionId)
        when(sessionResponse.value){
            is Success -> {
                val session = (sessionResponse.value as Success).invoke()
                return when (session.status) {
                    "CANCELED_BY_POS" -> {
                        val message = "Payment Session has been cancelled by POS Device"
                        val updatedSession = session.copy(messageForUser = message)
                        MutableStateFlow(Success(updatedSession))
                    }
                    "CREATED" -> {
                        val message = "Waiting for Payment"
                        val updatedSession = session.copy(messageForUser = message)
                        MutableStateFlow(Success(updatedSession))
                    }
                    "EXPIRED" -> {
                        val message = "Payment Session has been expired"
                        val updatedSession = session.copy(messageForUser = message)
                        MutableStateFlow(Success(updatedSession))
                    }
                    "AUTHORIZED" -> {
                        val message = "Payment has been authorized"
                        val updatedSession = session.copy(messageForUser = message)
                        MutableStateFlow(Success(updatedSession))
                    }
                    "DECLINED" -> {
                        val message = "Payment has been declined on POS Device"
                        val updatedSession = session.copy(messageForUser = message)
                        MutableStateFlow(Success(updatedSession))
                    }
                    "CANCELED_BY_USER" -> {
                        val message = "Payment Session has been cancelled by User"
                        val updatedSession = session.copy(messageForUser = message)
                        MutableStateFlow(Success(updatedSession))
                    }
                    else -> {
                        val message = "Payment Session has been ended due to Unknown Error"
                        val updatedSession = session.copy(messageForUser = message)
                        MutableStateFlow(Success(updatedSession))
                    }
                }
            }

            is Fail -> {
                return sessionResponse
            }

            else -> {
                return sessionResponse
            }
        }
    }
}
