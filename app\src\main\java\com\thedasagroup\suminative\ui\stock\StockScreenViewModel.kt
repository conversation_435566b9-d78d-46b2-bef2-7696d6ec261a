package com.thedasagroup.suminative.ui.stock

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.request.stock.ChangeStockRequest
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.StockRepository
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class StockScreenViewModel @AssistedInject constructor(
    @Assisted state: StockScreenState, val prefs: Prefs,
    val stockUseCase: StockUseCase,
    val changeStockUseCase: ChangeStockUseCase,
    val stockRepository: StockRepository
) : MavericksViewModel<StockScreenState>(state) {

    suspend fun getStockItems(): StateFlow<Async<StockItemsResponse>> {
        val flow = MutableStateFlow<Async<StockItemsResponse>>(Loading())
        setState {
            copy(stockItemsResponse = Loading())
        }
        stockUseCase().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(stockItemsResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(stockItemsResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun changeStock(request: ChangeStockRequest): StateFlow<Async<StockItemsResponse>> {
        val flow = MutableStateFlow<Async<StockItemsResponse>>(Loading())
        setState {
            copy(stockResponse = Loading())
        }
        changeStockUseCase(changeStockRequest = request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(stockItemsResponse = it(),
                        stockResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(stockResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    fun updateStock(stock: Int) {
        setState {
            copy(stock = stock)
        }
    }

    fun showUpdateStockDialog(stockItem : StockItem?){
        if(stockItem == null){
            setState {
                copy(showUpdateStockDialog = null, stock = 0)
            }
        }
        else {
            setState {
                copy(showUpdateStockDialog = stockItem)
            }
        }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<StockScreenViewModel, StockScreenState> {
        override fun create(state: StockScreenState): StockScreenViewModel
    }

    companion object :
        MavericksViewModelFactory<StockScreenViewModel, StockScreenState> by hiltMavericksViewModelFactory()
}

data class StockScreenState(
    val stockItemsResponse: Async<StockItemsResponse> = Uninitialized,
    val stockResponse: Async<StockItemsResponse> = Uninitialized,
    val stock : Int = 0,
    val showUpdateStockDialog: StockItem? = null
) : MavericksState