package com.thedasagroup.suminative.ui.rewards

import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.domain.rewards.AddPointsUseCase
import com.thedasagroup.suminative.domain.rewards.GetAllCustomersUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject

class RewardsViewModel @AssistedInject constructor(
    @Assisted state: RewardsState,
    private val getAllCustomersUseCase: GetAllCustomersUseCase,
    private val addPointsUseCase: AddPointsUseCase,
    val prefs: Prefs
) : MavericksViewModel<RewardsState>(state) {

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<RewardsViewModel, RewardsState> {
        override fun create(state: RewardsState): RewardsViewModel
    }

    companion object :
        MavericksViewModelFactory<RewardsViewModel, RewardsState> by hiltMavericksViewModelFactory()

    /**
     * Get all customers for a specific customer ID and business ID
     */
    suspend fun getAllCustomers(customerId: Int, businessId: Int? = null) {
        val actualBusinessId = businessId ?: prefs.loginResponse?.businesses?.id ?: 0
        
        setState {
            copy(
                getAllCustomersResponse = Loading(),
                isLoading = true,
                selectedCustomerId = customerId,
                selectedBusinessId = actualBusinessId
            )
        }

        getAllCustomersUseCase(customerId, actualBusinessId).execute { response ->
            when (response) {
                is Success -> {
                    copy(
                        getAllCustomersResponse = response(),
                        isLoading = false
                    )
                }
                else -> {
                    copy(
                        getAllCustomersResponse = Uninitialized,
                        isLoading = false
                    )
                }
            }
        }
    }

    /**
     * Add points to a customer's rewards account
     */
    suspend fun addPoints(
        customerId: Int,
        points: Int,
        businessId: Int? = null,
        orderId: Int? = null,
        description: String? = null
    ) {
        val actualBusinessId = businessId ?: prefs.loginResponse?.businesses?.id ?: 0
        
        setState {
            copy(
                addPointsResponse = Loading(),
                isLoading = true
            )
        }

        addPointsUseCase(
            customerId = customerId,
            businessId = actualBusinessId,
            points = points
        ).execute { response ->
            when (response) {
                is Success -> {
                    copy(
                        addPointsResponse = response(),
                        isLoading = false
                    )
                }
                else -> {
                    copy(
                        addPointsResponse = Uninitialized,
                        isLoading = false
                    )
                }
            }
        }
    }

    /**
     * Clear the add points response state
     */
    fun clearAddPointsResponse() {
        setState {
            copy(addPointsResponse = Uninitialized)
        }
    }

    /**
     * Clear the get all customers response state
     */
    fun clearGetAllCustomersResponse() {
        setState {
            copy(getAllCustomersResponse = Uninitialized)
        }
    }

    /**
     * Reset all state to initial values
     */
    fun resetState() {
        setState {
            copy(
                getAllCustomersResponse = Uninitialized,
                addPointsResponse = Uninitialized,
                isLoading = false,
                selectedCustomerId = null,
                selectedBusinessId = null
            )
        }
    }
}
