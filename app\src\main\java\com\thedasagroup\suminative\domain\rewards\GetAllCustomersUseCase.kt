package com.thedasagroup.suminative.domain.rewards

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.rewards.GetAllCustomersRequest
import com.thedasagroup.suminative.data.model.response.rewards.GetAllCustomersResponse
import com.thedasagroup.suminative.data.repo.RewardsRepository
import kotlinx.coroutines.flow.StateFlow

class GetAllCustomersUseCase(
    private val rewardsRepository: RewardsRepository
) {
    suspend operator fun invoke(customerId: Int, businessId: Int): StateFlow<Async<GetAllCustomersResponse>> {
        val request = GetAllCustomersRequest(
            customerId = customerId,
            businessId = businessId
        )
        return rewardsRepository.getAllCustomers(request)
    }
}
